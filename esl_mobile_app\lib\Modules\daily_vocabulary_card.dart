import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DailyVocabularyCard extends StatefulWidget {
  const DailyVocabularyCard({super.key});

  @override
  State<DailyVocabularyCard> createState() => _DailyVocabularyCardState();
}

class _DailyVocabularyCardState extends State<DailyVocabularyCard> {
  String _word = '';
  String _pronunciation = '';
  String _meaning = '';
  String _story = '';
  bool _isLoading = true;
  late FlutterTts flutterTts;

  @override
  void initState() {
    super.initState();
    flutterTts = FlutterTts();
    loadOrFetchVocabulary();
  }

  @override
  void dispose() {
    flutterTts.stop();
    super.dispose();
  }

  Future<void> _speakPronunciation() async {
    if (_word.isNotEmpty) {
      await flutterTts.setLanguage("en-US");
      await flutterTts.setPitch(1.0);
      await flutterTts.setSpeechRate(0.45);
      await flutterTts.speak(_word); // or use _pronunciation
    }
  }

  Future<void> loadOrFetchVocabulary() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T').first;

    final storedDate = prefs.getString('vocab_date');
    final storedWord = prefs.getString('vocab_word');
    final storedPronunciation = prefs.getString('vocab_pronunciation');
    final storedMeaning = prefs.getString('vocab_meaning');
    final storedStory = prefs.getString('vocab_story');

    if (storedDate == today &&
        storedWord != null &&
        storedPronunciation != null &&
        storedMeaning != null &&
        storedStory != null) {
      setState(() {
        _word = storedWord;
        _pronunciation = storedPronunciation;
        _meaning = storedMeaning;
        _story = storedStory;
        _isLoading = false;
      });
    } else {
      await fetchVocabulary(prefs, today);
    }
  }

  Future<void> fetchVocabulary(SharedPreferences prefs, String today) async {
    final apiKey = dotenv.env['OPENAI_API_KEY'];

    if (apiKey == null || apiKey.isEmpty) {
      setState(() {
        _word = '';
        _meaning = '';
        _story = "OpenAI API key not found. Add it to .env file.";
        _isLoading = false;
      });
      return;
    }

    const prompt = '''
Give me one advanced English vocabulary word with its meaning, IPA pronunciation, and a short funny story (maximum 50 words) to remember the word.

Format:
Word: <word>
Pronunciation: <IPA pronunciation>
Meaning: <meaning>
Story: <funny story in under 50 words>
''';

    try {
      final response = await http.post(
        Uri.parse("https://api.openai.com/v1/chat/completions"),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "model": "gpt-4o-mini-2024-07-18",
          "messages": [
            {"role": "user", "content": prompt}
          ],
          "max_tokens": 200,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        _parseAndStoreVocabulary(content, prefs, today);
      } else {
        setState(() {
          _story = "Error ${response.statusCode}: Failed to load vocabulary.";
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _story = "Exception occurred: $e";
        _isLoading = false;
      });
    }
  }

  Widget _buildBackgroundImage() {
    return SvgPicture.asset(
      'assets/Vocabulary_card_bg.svg',
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
    );
  }

  void _parseAndStoreVocabulary(
      String content, SharedPreferences prefs, String today) {
    final lines = content.split('\n');
    String word = '', pronunciation = '', meaning = '', story = '';

    for (var line in lines) {
      if (line.toLowerCase().startsWith('word:')) {
        word = line.substring(5).trim();
      } else if (line.toLowerCase().startsWith('pronunciation:')) {
        pronunciation = line.substring(14).trim();
      } else if (line.toLowerCase().startsWith('meaning:')) {
        meaning = line.substring(8).trim();
      } else if (line.toLowerCase().startsWith('story:')) {
        story = line.substring(6).trim();
      }
    }

    prefs.setString('vocab_date', today);
    prefs.setString('vocab_word', word);
    prefs.setString('vocab_pronunciation', pronunciation);
    prefs.setString('vocab_meaning', meaning);
    prefs.setString('vocab_story', story);

    setState(() {
      _word = word;
      _pronunciation = pronunciation;
      _meaning = meaning;
      _story = story;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.all(16),
      child: ClipRRect(
        // Ensures image respects card's border radius
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade50,
                Colors.purple.shade50,
                Colors.teal.shade50,
              ],
            ),
          ),
          child: Stack(
            children: [
              // Try to load SVG, fallback to simple SVG if original fails
              Positioned.fill(
                child: _buildBackgroundImage(),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Word:',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Poppins',
                                fontSize: 16),
                          ),
                          Text(
                            _word,
                            style: const TextStyle(
                              color: Colors.teal,
                              fontSize: 20,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Pronunciation: ',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'Poppins',
                                    fontSize: 16),
                              ),
                              Expanded(
                                child: Text(
                                  _pronunciation,
                                  style: const TextStyle(
                                      color: Colors.indigo,
                                      fontStyle: FontStyle.italic),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.volume_up),
                                color: Colors.blueAccent,
                                onPressed: _speakPronunciation,
                                tooltip: 'Hear it!',
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            'Meaning:',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Poppins',
                                fontSize: 16),
                          ),
                          Text(
                            _meaning,
                            style: const TextStyle(color: Colors.deepPurple),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Story:',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Poppins',
                                fontSize: 16),
                          ),
                          Text(
                            _story,
                            style: const TextStyle(fontSize: 15),
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// class DailyVocabularyCard extends StatefulWidget {
//   const DailyVocabularyCard({super.key});

//   @override
//   State<DailyVocabularyCard> createState() => _DailyVocabularyCardState();
// }

// class _DailyVocabularyCardState extends State<DailyVocabularyCard> {
//   Map<String, dynamic>? _vocabData;
//   bool _isLoading = true;
//   String? _error;
//   final FlutterTts _flutterTts = FlutterTts();

//   @override
//   void initState() {
//     super.initState();
//     _fetchVocabulary();
//   }

//   Future<void> _fetchVocabulary() async {
//     try {
//       final response = await http
//           .get(Uri.parse('http://talktoai.in:4009/latest_notification'))
//           .timeout(const Duration(seconds: 10));

//       if (response.statusCode == 200) {
//         final data = jsonDecode(response.body);
//         setState(() {
//           _vocabData = data;
//           _isLoading = false;
//         });
//       } else {
//         throw Exception('Failed to load data: ${response.statusCode}');
//       }
//     } catch (e) {
//       setState(() {
//         _error = e.toString();
//         _isLoading = false;
//       });
//     }
//   }

//   Future<void> _speakWord(String word) async {
//     await _flutterTts.setLanguage("en-US");
//     await _flutterTts.setPitch(1.0);
//     await _flutterTts.speak(word);
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (_isLoading) {
//       return const Center(child: CircularProgressIndicator());
//     }

//     if (_error != null) {
//       return Center(child: Text("Error: $_error"));
//     }

//     if (_vocabData == null) {
//       return const Center(child: Text("No data found."));
//     }

//     final word = _vocabData!['word'] ?? 'N/A';
//     final meaning = _vocabData!['meaning'] ?? 'N/A';
//     final story = _vocabData!['story'] ?? 'N/A';

//     return Card(
//       elevation: 4,
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text("Word of the Day",
//                 style: Theme.of(context).textTheme.titleLarge),
//             const SizedBox(height: 12),
//             Row(
//               children: [
//                 Expanded(
//                   child: Text("🔤 Word: $word",
//                       style: const TextStyle(
//                           fontSize: 18, fontWeight: FontWeight.bold)),
//                 ),
//                 IconButton(
//                   icon: const Icon(Icons.volume_up),
//                   onPressed: () => _speakWord(word),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 8),
//             Text("📘 Meaning: $meaning",
//                 style: const TextStyle(fontSize: 16)),
//             const SizedBox(height: 12),
//             Text("📖 Funny Story:\n$story",
//                 style: const TextStyle(fontSize: 15)),
//           ],
//         ),
//       ),
//     );
//   }
// }
