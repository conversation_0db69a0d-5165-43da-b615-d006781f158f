class PlanData {
  static final List<Map<String, dynamic>> plans = [
    {
      'name': 'Monthly Plan',
      'price': '555', // INR price
      'oldPrice': '1500', // Higher old price for discount effect
      'price-USD': '13', // USD price (555 INR / 80 ≈ 7, rounded up for effect)
      'oldPrice-USD': '20',
      'detail': [
        "1 Month access to all unlimited Mock tests.",
        "1 Month access to all learning Resources.",
        "1 Month access to unlimited AI tutor messages.",
        "1 Month access to all exercises.",
        "Complete result & feedback.",
        "All previous results tracking.",
        "Provide PDF Files and documents"
      ],
      'image': 'assets/il4.jpg',
      'color': '#F7CE56',
      'razorpayProductId': 'prod_MonthlyPlan',
      'plan_id': 'PLAN_MONTHLY'
    },
    {
      'name': '6 Month Plan',
      'price': '1999',
      'oldPrice': '4000',
      'price-USD': '25',
      'oldPrice-USD': '50',
      'detail': [
        "6 Months access to all unlimited Mock tests.",
        "6 Months access to all learning Resources.",
        "6 Months access to unlimited AI tutor messages.",
        "6 Months access to all exercises.",
        "Complete result & feedback.",
        "All previous results tracking.",
        "Provide PDF Files and documents"
      ],
      'image': 'assets/il8.jpg',
      'color': '#7253A4',
      'razorpayProductId': 'prod_6MonthPlan',
      'plan_id': 'PLAN_6MONTH'
    },
  ];
}