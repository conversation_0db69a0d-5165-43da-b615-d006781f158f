import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';
import '../widgets/star_loading_animation.dart';
import '../widgets/wave_recording_animation.dart';

// SubtitleItem class to represent subtitle with timestamps
class SubtitleItem {
  final String text;
  final int startTime; // in milliseconds
  final int endTime; // in milliseconds

  SubtitleItem({
    required this.text,
    required this.startTime,
    required this.endTime,
  });

  factory SubtitleItem.fromJson(Map<String, dynamic> json) {
    return SubtitleItem(
      text: json['text'] ?? '',
      startTime: json['start_time'] ?? 0,
      endTime: json['end_time'] ?? 0,
    );
  }
}

// DynamicSubtitleWidget for dynamic subtitle display
class DynamicSubtitleWidget extends StatefulWidget {
  final VideoPlayerController videoController;
  final List<SubtitleItem> subtitles;
  final Color textColor;
  final Color accentColor;

  const DynamicSubtitleWidget({
    Key? key,
    required this.videoController,
    required this.subtitles,
    required this.textColor,
    required this.accentColor,
  }) : super(key: key);

  @override
  _DynamicSubtitleWidgetState createState() => _DynamicSubtitleWidgetState();
}

class _DynamicSubtitleWidgetState extends State<DynamicSubtitleWidget> {
  String _currentSubtitle = '';
  bool _isVideoPlaying = false;

  @override
  void initState() {
    super.initState();
    widget.videoController.addListener(_updateVideoState);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startSubtitleSync();
    });
  }

  void _updateVideoState() {
    setState(() {
      _isVideoPlaying = widget.videoController.value.isPlaying;
    });
  }

  void _startSubtitleSync() async {
    while (mounted) {
      if (_isVideoPlaying) {
        final position = await widget.videoController.position;
        if (position != null) {
          final currentTime = position.inMilliseconds;
          final subtitle = widget.subtitles.firstWhere(
            (sub) => currentTime >= sub.startTime && currentTime <= sub.endTime,
            orElse: () => SubtitleItem(text: '', startTime: 0, endTime: 0),
          );
          setState(() {
            _currentSubtitle = subtitle.text;
          });
        }
      }
      await Future.delayed(Duration(milliseconds: 100));
    }
  }

  @override
  void dispose() {
    widget.videoController.removeListener(_updateVideoState);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.yellow,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.black.withOpacity(0.1),
              const Color.fromARGB(255, 223, 152, 236).withOpacity(0.1),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Text(
              _currentSubtitle.isNotEmpty ? _currentSubtitle : 'No subtitle',
              style: TextStyle(
                color: widget.textColor,
                fontSize: 18,
                fontWeight: _isVideoPlaying ? FontWeight.w800 : FontWeight.w600,
                shadows: _isVideoPlaying
                    ? [
                        Shadow(
                          blurRadius: 2.0,
                          color: widget.accentColor.withOpacity(0.5),
                          offset: Offset(1.0, 1.0),
                        ),
                      ]
                    : [],
              ),
            )
                .animate(
                  onPlay: (controller) =>
                      _isVideoPlaying ? controller.repeat() : controller.stop(),
                )
                .shimmer(
                  duration: 2000.ms,
                  color: widget.accentColor.withOpacity(0.2),
                ),
          ),
        ),
      ),
    );
  }
}

class ListeningScreen extends StatefulWidget {
  const ListeningScreen({super.key});

  @override
  _ListeningScreenState createState() => _ListeningScreenState();
}

class _ListeningScreenState extends State<ListeningScreen> {
  String? title;
  List<SubtitleItem>? subtitles; // Changed to List<SubtitleItem>
  String? videoPath;
  VideoPlayerController? videoController;
  ChewieController? chewieController;
  bool isRecording = false;
  String? recordingPath;
  String? transcript;
  String? feedback;
  bool isLoading = false;
  String? errorMessage;
  final AudioRecorder record = AudioRecorder();
  late ConfettiController _confettiController;
  bool _showSuccess = false;
  double _recordingLevel = 0.0;
  Timer? _recordingTimer;
  final Random _random = Random();
  bool _showInitialGuide = true;

  // Pastel color scheme
  static const Color primaryColor = Color(0xFFB39DDB); // Pastel purple
  static const Color secondaryColor = Color(0xFF80CBC4); // Pastel teal
  static const Color accentColor = Color(0xFFFFF59D); // Pastel yellow
  static const Color backgroundColor = Color(0xFFFAFAFA); // Very light gray
  static const Color cardColor = Color(0xFFFFFFFF); // White cards
  static const Color textColor = Color(0xFF424242); // Dark gray text
  static const Color secondaryTextColor = Color(0xFF757575); // Gray text

  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 3));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showGuideDialog();
    });
  }

  void _showGuideDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                primaryColor.withOpacity(0.1),
                secondaryColor.withOpacity(0.1)
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How to Use This Assessment',
                  style: TextStyle(
                    color: primaryColor,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildGuideStep(1, 'Watch the video carefully'),
                _buildGuideStep(2, 'Listen to the spoken content'),
                _buildGuideStep(3, 'Record yourself repeating what you heard'),
                _buildGuideStep(4, 'Submit your recording for feedback'),
                _buildGuideStep(5, 'Review your transcript and feedback'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: accentColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Make sure you are in a quiet environment for best results!',
                    style: TextStyle(
                      color: secondaryColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _showInitialGuide = false;
                      });
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Got it!',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGuideStep(int number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$number',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: textColor,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> fetchAssessment() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
      _showSuccess = false;
      chewieController?.dispose();
      videoController?.dispose();
      videoController = null;
      chewieController = null;
      videoPath = null;
      subtitles = null; // Reset subtitles
    });

    try {
      var response = await http
          .get(Uri.parse('http://talktoai.in:4008/listening_start_assessment'))
          .timeout(const Duration(seconds: 30), onTimeout: () {
        throw TimeoutException('Request to API timed out');
      });

      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);
        title = data['title'];
        // Handle subtitle data
        if (data['subtitles'] != null && data['subtitles'] is List) {
          subtitles = (data['subtitles'] as List)
              .map((item) => SubtitleItem.fromJson(item))
              .toList();
        } else if (data['subtitle'] != null && data['subtitle'] is String) {
          // Fallback for single subtitle string
          subtitles = [
            SubtitleItem(
              text: data['subtitle'],
              startTime: 0,
              endTime: 9999999, // Large end time to cover entire video
            ),
          ];
        } else {
          subtitles = [];
        }
        String videoBase64 = data['video_base64'];

        if (videoBase64.isEmpty) {
          throw Exception('Empty video data received from API');
        }

        List<int> videoBytes = base64Decode(videoBase64);
        Directory tempDir = await getTemporaryDirectory();
        String tempPath = tempDir.path;
        File tempVideoFile = File(
            '$tempPath/video_${DateTime.now().millisecondsSinceEpoch}.mp4');

        await tempVideoFile.writeAsBytes(videoBytes, flush: true);

        if (!await tempVideoFile.exists() ||
            await tempVideoFile.length() == 0) {
          throw Exception('Failed to create valid video file');
        }

        print('Video file saved at: ${tempVideoFile.path}');
        print('Video file exists: ${await tempVideoFile.exists()}');
        print('Video file size: ${await tempVideoFile.length()} bytes');

        videoController = VideoPlayerController.file(tempVideoFile);
        await videoController!.initialize().timeout(const Duration(seconds: 10),
            onTimeout: () {
          throw Exception('Video initialization timed out');
        });

        if (!videoController!.value.isInitialized) {
          throw Exception('Video controller failed to initialize');
        }

        print(
            'Video controller initialized: ${videoController!.value.isInitialized}');
        print('Video duration: ${videoController!.value.duration}');
        print('Video aspect ratio: ${videoController!.value.aspectRatio}');
        if (videoController!.value.hasError) {
          throw Exception(
              'Video controller error: ${videoController!.value.errorDescription}');
        }

        chewieController = ChewieController(
          videoPlayerController: videoController!,
          autoPlay: false,
          looping: false,
          allowFullScreen: true,
          showControls: true,
          showControlsOnInitialize: true,
          allowPlaybackSpeedChanging: true,
          allowMuting: true,
          materialProgressColors: ChewieProgressColors(
            playedColor: primaryColor,
            handleColor: accentColor,
            backgroundColor: Colors.grey,
            bufferedColor: Colors.grey.shade300,
          ),
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Video playback error: $errorMessage',
                    style: TextStyle(color: textColor),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        );

        print('Chewie controller initialized: ${chewieController != null}');

        setState(() {
          videoPath = tempVideoFile.path;
          isLoading = false;
        });
      } else {
        throw Exception('Failed to load assessment: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      print('Error fetching assessment: $e');
      print('Stack trace: $stackTrace');
      setState(() {
        errorMessage = 'Error loading video: $e';
        isLoading = false;
      });
    }
  }

  Future<void> startRecording() async {
    if (await record.hasPermission()) {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      String recordingPath = '$tempPath/recording.wav';
      await record.start(const RecordConfig(), path: recordingPath);

      _recordingTimer =
          Timer.periodic(const Duration(milliseconds: 200), (timer) {
        setState(() {
          _recordingLevel = 0.2 + _random.nextDouble() * 0.8;
        });
      });

      setState(() {
        isRecording = true;
        this.recordingPath = recordingPath;
      });
    } else {
      setState(() {
        errorMessage = 'Microphone permission denied';
      });
    }
  }

  Future<void> stopRecording() async {
    _recordingTimer?.cancel();
    final path = await record.stop();
    setState(() {
      isRecording = false;
      recordingPath = path;
      _recordingLevel = 0.0;
    });
  }

  Future<void> submitResponse() async {
    if (recordingPath == null) {
      setState(() {
        errorMessage = 'No recording available to submit';
      });
      return;
    }
    setState(() {
      isLoading = true;
      errorMessage = null;
    });
    try {
      File recordingFile = File(recordingPath!);
      List<int> audioBytes = await recordingFile.readAsBytes();
      String audioBase64 = base64Encode(audioBytes);
      var submitResponse = await http.post(
        Uri.parse('http://talktoai.in:4008/listening_submit_response'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'title': title,
          'audio_base64': audioBase64,
        }),
      );
      if (submitResponse.statusCode == 200) {
        var feedbackData = jsonDecode(submitResponse.body);
        transcript = feedbackData['transcript'];
        feedback = feedbackData['feedback'];
        _confettiController.play();
        setState(() {
          _showSuccess = true;
        });
        Future.delayed(const Duration(seconds: 3), () {
          setState(() {
            _showSuccess = false;
          });
        });
      } else {
        setState(() {
          errorMessage =
              'Failed to submit response: ${submitResponse.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error submitting response: $e';
      });
    }
    setState(() {
      isLoading = false;
    });
  }

  @override
  void dispose() {
    videoController?.pause();
    videoController?.dispose();
    chewieController?.dispose();
    record.dispose();
    _confettiController.dispose();
    _recordingTimer?.cancel();
    if (videoPath != null) {
      try {
        File(videoPath!).delete();
      } catch (e) {
        print('Error deleting video file: $e');
      }
    }
    if (recordingPath != null) {
      try {
        File(recordingPath!).delete();
      } catch (e) {
        print('Error deleting recording file: $e');
      }
    }
    super.dispose();
  }

  Widget _buildInitialState() {
    return Card(
      color: cardColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor.withOpacity(0.1),
              secondaryColor.withOpacity(0.1),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.headphones,
                  size: 64,
                  color: primaryColor,
                ),
              ).animate().scale(duration: 1.seconds).shimmer(),
              const SizedBox(height: 24),
              Text(
                'Listening Assessment',
                style: TextStyle(
                  color: textColor,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 500.ms),
              const SizedBox(height: 16),
              Text(
                'Test your listening comprehension by repeating what you hear',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: secondaryTextColor,
                  fontSize: 18,
                ),
              ).animate().fadeIn(duration: 700.ms),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: fetchAssessment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                  shadowColor: primaryColor.withOpacity(0.3),
                ),
                child: const Text(
                  'Start Assessment',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ).animate().scale(delay: 300.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoSection() {
    if (videoController == null ||
        !videoController!.value.isInitialized ||
        chewieController == null) {
      return Card(
        color: cardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                primaryColor.withOpacity(0.1),
                secondaryColor.withOpacity(0.1),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: primaryColor),
                  const SizedBox(height: 16),
                  Text(
                    'Loading video...',
                    style: TextStyle(color: textColor, fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        Card(
          color: cardColor,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 2,
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: AspectRatio(
                aspectRatio: videoController!.value.aspectRatio,
                child: Stack(
                  children: [
                    Chewie(controller: chewieController!),
                    Positioned.fill(
                      child: IgnorePointer(
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.5),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 16,
                      left: 16,
                      right: 16,
                      child: IgnorePointer(
                        child: Text(
                          title ?? 'Listening Exercise',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                color: Colors.black,
                                blurRadius: 6,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        if (subtitles != null && subtitles!.isNotEmpty)
          DynamicSubtitleWidget(
            videoController: videoController!,
            subtitles: subtitles!,
            textColor: textColor,
            accentColor: accentColor,
          ),
      ],
    );
  }

  Widget _buildRecordingSection() {
    return Column(
      children: [
        Card(
          color: cardColor,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor.withOpacity(0.1),
                  secondaryColor.withOpacity(0.1),
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Text(
                    'Record Your Response',
                    style: TextStyle(
                      color: textColor,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  if (isRecording)
                    Column(
                      children: [
                        Container(
                          width: 200,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 2,
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child:
                                WaveRecordingAnimation(level: _recordingLevel),
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: stopRecording,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red.withOpacity(0.8),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                vertical: 16, horizontal: 24),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(Icons.stop, size: 24),
                              SizedBox(width: 8),
                              Text(
                                'Stop Recording',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  else if (recordingPath != null)
                    Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: secondaryColor.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check_circle,
                            color: secondaryColor,
                            size: 48,
                          ),
                        ),
                        const SizedBox(height: 20),
                        ElevatedButton(
                          onPressed: submitResponse,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: secondaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                vertical: 16, horizontal: 24),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: const Text(
                            'Submit Response',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    )
                  else
                    ElevatedButton(
                      onPressed: startRecording,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            vertical: 16, horizontal: 24),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.mic, size: 24),
                          const SizedBox(width: 8),
                          Text(
                            'Start Recording',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsSection() {
    return Column(
      children: [
        const SizedBox(height: 16),
        Card(
          color: cardColor,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor.withOpacity(0.1),
                  secondaryColor.withOpacity(0.1),
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: primaryColor.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.transcribe,
                          color: primaryColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Your Transcript:',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      transcript ?? '',
                      style: TextStyle(
                        fontSize: 16,
                        color: textColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: secondaryColor.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.feedback,
                          color: secondaryColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Feedback:',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: secondaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      feedback ?? '',
                      style: TextStyle(
                        fontSize: 16,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Positioned(
            top: -50,
            right: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: primaryColor.withOpacity(0.1),
              ),
            ),
          ),
          Positioned(
            bottom: -100,
            left: -50,
            child: Container(
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: secondaryColor.withOpacity(0.1),
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(
                  top: 40.0, left: 16, right: 16, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (isLoading)
                    Card(
                      color: cardColor,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withOpacity(0.1),
                              secondaryColor.withOpacity(0.1),
                            ],
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const StarLoadingAnimation(
                                  size: 80,
                                ),
                                const SizedBox(height: 24),
                                Text(
                                  "Loading Assessment...",
                                  style: TextStyle(
                                    color: textColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  else if (errorMessage != null)
                    Card(
                      color: cardColor,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withOpacity(0.1),
                              secondaryColor.withOpacity(0.1),
                            ],
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Center(
                            child: Column(
                              children: [
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    color: Colors.red.withOpacity(0.1),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.error_outline,
                                    color: Colors.red,
                                    size: 48,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  errorMessage!,
                                  style: TextStyle(
                                    color: textColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton(
                                  onPressed: fetchAssessment,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: primaryColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 32, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: const Text('Try Again'),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  else if (videoController == null)
                    _buildInitialState()
                  else ...[
                    _buildVideoSection(),
                    const SizedBox(height: 24),
                    _buildRecordingSection(),
                    if (transcript != null && feedback != null)
                      _buildResultsSection(),
                  ],
                ],
              ),
            ),
          ),
          if (_showSuccess)
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.explosive,
                shouldLoop: false,
                colors: const [
                  Color(0xFFB39DDB), // Pastel purple
                  Color(0xFF80CBC4), // Pastel teal
                  Color(0xFFFFF59D), // Pastel yellow
                ],
              ),
            ),
        ],
      ),
    );
  }
}
