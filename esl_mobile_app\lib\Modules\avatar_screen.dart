import 'package:flutter/material.dart';
import 'package:o3d/o3d.dart';
import 'dart:async';
import '../widgets/star_loading_animation.dart';

class ThreeDModelPage extends StatefulWidget {
  const ThreeDModelPage({super.key});

  @override
  State<ThreeDModelPage> createState() => _ThreeDModelPageState();
}

class _ThreeDModelPageState extends State<ThreeDModelPage> {
  final O3DController controller = O3DController();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  final bool _useSolidBackground = false;
  final bool _isAnimationPlaying = true;

  final String _currentModelPath = 'assets/models/davidfinalglb.glb';

  final bool _debugMode = true;
  final String _currentAnimation = 'Standing_Idle';
  Timer? _autoRotateTimer;
  double _rotationAngle = 0;

  void _debugLog(String message) {
    if (_debugMode) {
      debugPrint('ESL Avatar: $message');
    }
  }

  @override
  void initState() {
    super.initState();
    controller.logger = (message) {
      _debugLog('O3D: $message');
    };

    _checkLocalModel();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 1), () {
        controller.cameraOrbit(0, 75, 100);
        controller.cameraTarget(0, 1.2, 0);
        _startAutoRotation();
        _applyAnimation();
      });

      Future.delayed(const Duration(seconds: 10), () {
        if (_isLoading) {
          setState(() {
            _hasError = true;
            _errorMessage = 'Failed to load 3D model in time.';
          });
        }
      });
    });
  }

  void _checkLocalModel() {
    _debugLog('Assuming asset model is available at $_currentModelPath');
    setState(() {
      _isLoading = false;
      _hasError = false;
      _errorMessage = '';
    });
  }

  @override
  void dispose() {
    _autoRotateTimer?.cancel();
    super.dispose();
  }

  void _startAutoRotation() {
    _autoRotateTimer?.cancel();
    _autoRotateTimer =
        Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (!_isLoading && !_hasError) {
        setState(() {
          _rotationAngle = (_rotationAngle + 1) % 360;
        });
        controller.cameraOrbit(_rotationAngle, 75, 100);
      }
    });
  }

  void _applyAnimation() {
    if (_isAnimationPlaying) {
      try {
        controller.animationName = _currentAnimation;
        controller.play();
        _debugLog('Playing animation: $_currentAnimation');
      } catch (e) {
        _debugLog('Animation error: $e');
      }
    } else {
      controller.pause();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        if (_isLoading)
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF1A1F35), // Dark blue background
                  Color(0xFF2D3250), // Slightly lighter blue
                ],
                stops: [0.0, 1.0],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const StarLoadingAnimation(
                    size: 80,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "Loading 3D model...",
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      shadows: const [
                        Shadow(
                          color: Color(0xFF9C27B0), // Purple shadow
                          blurRadius: 4,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        if (_hasError)
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF1A1F35), // Dark blue background
                  Color(0xFF2D3250), // Slightly lighter blue
                ],
                stops: [0.0, 1.0],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error_outline,
                      color: Color(0xFF9C27B0), // Purple color
                      size: 48),
                  const SizedBox(height: 16),
                  Text(
                    "Error loading model: $_errorMessage",
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      shadows: const [
                        Shadow(
                          color: Color(0xFF9C27B0), // Purple shadow
                          blurRadius: 4,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        O3D.asset(
          key: ValueKey(_currentModelPath),
          src: _currentModelPath,
          controller: controller,
          cameraControls: false,
          backgroundColor:
              _useSolidBackground ? Colors.white : Colors.transparent,
          ar: false,
          onWebViewCreated: (webViewController) {
            setState(() {
              _isLoading = false;
              _hasError = false;
              _errorMessage = '';
            });
            if (_isAnimationPlaying) {
              controller.availableAnimations().then((animations) {
                if (animations.isNotEmpty) {
                  _debugLog('Available animations: $animations');
                  controller.play();
                } else {
                  _debugLog('No animations available in model');
                }
              }).catchError((error) {
                _debugLog('Error checking animations: $error');
              });
            }
          },
        ),
      ],
    );
  }
}
