import 'dart:math';
import 'package:flutter/material.dart';

class SparklingStars extends StatefulWidget {
  const SparklingStars({super.key});

  @override
  State<SparklingStars> createState() => _SparklingStarsState();
}

class _SparklingStarsState extends State<SparklingStars>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(
          seconds: 500), // Further increased duration for even slower animation
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(3, (index) {
          final offset =
              index * 0.3; // Increased offset for more staggered animation
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final value = (_controller.value + offset) % 1.0;
              // Extremely subtle opacity transition with minimal range
              final opacity = 0.8 + 0.2 * (1 - (value - 0.5).abs() * 2);
              // Extremely subtle scale transition with minimal range
              final scale = 1.0 + 0.08 * (1 - (value - 0.5).abs() * 2);
              return Opacity(
                opacity: opacity,
                child: Transform.scale(
                  scale: scale,
                  child: CustomPaint(
                    size: const Size(20, 20),
                    painter: StarPainter(),
                  ),
                ),
              );
            },
          );
        }),
      );
}

class StarPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    drawStar(canvas, Offset(size.width / 2, size.height / 2), size.width / 2, 0,
        paint);
  }

  void drawStar(Canvas canvas, Offset center, double radius, double rotation,
      Paint paint) {
    final path = Path();
    final double halfRadius = radius / 2;

    // Calculate the 5 outer points of the star
    for (int i = 0; i < 5; i++) {
      final double angle = rotation + (i * 2 * pi / 5);
      final double x = center.dx + radius * cos(angle);
      final double y = center.dy + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // Calculate inner points of the star
      final double innerAngle = rotation + (i * 2 * pi / 5) + (pi / 5);
      final double innerX = center.dx + halfRadius * cos(innerAngle);
      final double innerY = center.dy + halfRadius * sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();

    // Add glow effect
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.8);
    canvas.drawPath(path, paint);

    // Draw the core of the star
    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
