import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import '../user_provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:math';

class HelperBot extends StatefulWidget {
  const HelperBot({super.key});

  @override
  _HelperBotState createState() => _HelperBotState();
}

class _HelperBotState extends State<HelperBot> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _waveAnimation;
  late AnimationController _starsController;
  String? _sessionId;
  List<String> _botMessages = [];
  bool _isRecording = false;
  bool _isLoading = false;
  bool _isActive = false;
  bool _isBotSpeaking = false;
  final AudioPlayer _audioPlayer = AudioPlayer();
  final AudioRecorder _audioRecorder = AudioRecorder();
  String? _recordedAudioPath;

  // OpenAI Whisper API Key (replace with your valid key)
  static const String _openAIApiKey =
      "********************************************************************************************************************************************************************"; // Replace this
  static const String _whisperApiUrl =
      "https://api.openai.com/v1/audio/transcriptions";

  @override
  void initState() {
    super.initState();
    // Wave animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat();
    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller)
      ..addListener(() {
        setState(() {});
      });

    // Stars animation controller - initially stopped
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8), // Slower animation for stars
    );

    _startSession();
    _audioPlayer.onPlayerComplete.listen((event) {
      setState(() {
        _isBotSpeaking = false;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _starsController.dispose();
    _audioPlayer.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  Future<void> _startSession() async {
    setState(() => _isLoading = true);
    final userId = int.tryParse(
        Provider.of<UserProvider>(context, listen: false).userId ?? '0');
    if (userId == null) {
      setState(() {
        _botMessages = ['User ID not found.'];
        _isLoading = false;
      });
      return;
    }

    try {
      final response = await http.post(
        Uri.parse('http://talktoai.in:4004/api/start_help'),
        headers: {'Content-Type': 'application/json; charset=utf-8'},
        body: jsonEncode({'user_id': userId}),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        setState(() {
          _sessionId = data['session_id'];
          _botMessages = [
            data['bot_greeting_text'] ?? 'Hello! How can I assist you today?'
          ];
          _isActive = true;
          print('Received bot text: ${_botMessages.first}');
        });
        if (data['bot_greeting_audio_base64'] != null) {
          await _playAudio(data['bot_greeting_audio_base64']);
        }
      } else {
        setState(() {
          _botMessages = ['Failed to start session: ${response.body}'];
          _isLoading = false;
        });
        print(
            'Response status: ${response.statusCode}, Body: ${response.body}');
      }
    } catch (e) {
      setState(() {
        _botMessages = ['Error starting session: $e'];
        _isLoading = false;
      });
      print('Error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<String?> _transcribeAudio(String audioPath) async {
    try {
      final request = http.MultipartRequest('POST', Uri.parse(_whisperApiUrl));
      request.headers['Authorization'] = 'Bearer $_openAIApiKey';
      request.headers['Content-Type'] = 'multipart/form-data';

      request.fields['model'] = 'whisper-1';
      request.files.add(await http.MultipartFile.fromPath('file', audioPath));

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        final transcription = data['text'];
        if (transcription != null && transcription.isNotEmpty) {
          print('Transcription: $transcription');
          return transcription;
        } else {
          print('No transcription available');
          setState(() {
            _botMessages.add('No speech detected in the audio.');
          });
          return null;
        }
      } else {
        final errorData = jsonDecode(responseBody);
        final errorMessage = errorData['error']?['message'] ??
            'Unknown error during transcription.';
        print('Whisper API failed: ${response.statusCode}, $errorMessage');
        setState(() {
          _botMessages
              .add('Transcription failed: $errorMessage. Check your API key.');
        });
        return null;
      }
    } catch (e) {
      print('Error transcribing audio: $e');
      setState(() {
        _botMessages.add('Error during transcription: $e');
      });
      return null;
    }
  }

  Future<void> _sendTextQuery(String queryText) async {
    if (_sessionId == null) return;

    setState(() => _isLoading = true);
    final userId = int.tryParse(
        Provider.of<UserProvider>(context, listen: false).userId ?? '0');
    if (userId == null) {
      setState(() {
        _botMessages.add('User ID not found.');
        _isLoading = false;
      });
      return;
    }

    try {
      final response = await http.post(
        Uri.parse('http://talktoai.in:4004/api/ask'),
        headers: {'Content-Type': 'application/json; charset=utf-8'},
        body: jsonEncode({
          'user_id': userId,
          'session_id': _sessionId,
          'user_query_text': queryText,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        setState(() {
          _botMessages.add(data['bot_reply_text'] ?? 'No reply text.');
          print('Received reply text: ${data['bot_reply_text']}');
        });
        if (data['bot_reply_audio_base64'] != null) {
          await _playAudio(data['bot_reply_audio_base64']);
        }
      } else {
        setState(() {
          _botMessages.add('Failed to get response: ${response.body}');
          _isLoading = false;
        });
        print(
            'Response status: ${response.statusCode}, Body: ${response.body}');
      }
    } catch (e) {
      setState(() {
        _botMessages.add('Error sending query: $e');
        _isLoading = false;
      });
      print('Error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _playAudio(String base64Audio) async {
    try {
      setState(() => _isBotSpeaking = true);
      await _audioPlayer.play(BytesSource(base64Decode(base64Audio)));
    } catch (e) {
      print('Error playing audio: $e');
      setState(() => _isBotSpeaking = false);
    }
  }

  Future<void> _startRecording() async {
    if (await _audioRecorder.hasPermission()) {
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/audio_recording.m4a';
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );
      setState(() {
        _isRecording = true;
        _recordedAudioPath = path;
      });
      _starsController.repeat(); // Start star animation
    } else {
      setState(() {
        _botMessages.add('Microphone permission denied.');
      });
    }
  }

  Future<void> _stopRecording() async {
    final path = await _audioRecorder.stop();
    setState(() {
      _isRecording = false;
      _recordedAudioPath = path;
    });
    _starsController.stop();
    _starsController.reset(); // Stop and reset star animation
    if (_recordedAudioPath != null) {
      final transcribedText = await _transcribeAudio(_recordedAudioPath!);
      if (transcribedText != null && transcribedText.isNotEmpty) {
        await _sendTextQuery(transcribedText);
      } else {
        setState(() {
          _botMessages.add('No speech detected or transcription failed.');
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _isActive ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF050A1A), // Very dark blue/black background
                  Color(0xFF0B0F2E), // Dark blue background
                ],
                stops: [0.0, 1.0],
              ),
            ),
            child: const SizedBox.expand(),
          ),
          // Wave animation
          AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: WavePainter(
                  waveAnimationValue: _waveAnimation.value,
                  starsAnimationValue: _starsController.value,
                  isRecording: _isRecording,
                ),
                child: Container(),
              );
            },
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading)
                  const CircularProgressIndicator(
                      color: Color(0xFF9C27B0)), // Purple color
                if (!_isLoading)
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16.0),
                      itemCount: _botMessages.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10.0),
                          child: ChatBubble(
                            message: _botMessages[index],
                            isSentByBot: true,
                          ),
                        );
                      },
                    ),
                  ),
                if (!_isBotSpeaking)
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: SizedBox(
                      width: 200,
                      height: 200,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Positioned(
                            top: 20,
                            child: Text(
                              _isRecording
                                  ? "Tap to stop recording"
                                  : "Tap to start recording",
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                shadows: [
                                  Shadow(
                                    color: Color(0xFF9C27B0),
                                    blurRadius: 4,
                                    offset: Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: (_isLoading || _isBotSpeaking)
                                ? null
                                : () {
                                    if (_isRecording) {
                                      _stopRecording();
                                    } else {
                                      _startRecording();
                                    }
                                  },
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [
                                    const Color(0xFF3F51B5).withOpacity(
                                        _isRecording ? 0.8 : 0.4), // Indigo
                                    const Color(0xFF9C27B0).withOpacity(
                                        _isRecording ? 0.6 : 0.2), // Purple
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFF9C27B0).withOpacity(
                                        _isRecording
                                            ? 0.7
                                            : 0.3), // Purple glow
                                    blurRadius: 20,
                                    spreadRadius: 5,
                                  ),
                                ],
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1.5,
                                ),
                              ),
                              child: Icon(
                                _isRecording ? Icons.stop : Icons.mic,
                                color: Colors.white,
                                size: 40,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ChatBubble extends StatelessWidget {
  final String message;
  final bool isSentByBot;

  const ChatBubble(
      {super.key, required this.message, required this.isSentByBot});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isSentByBot
              ? [
                  const Color(0xFF3F51B5), // Indigo
                  const Color(0xFF9C27B0), // Purple
                ]
              : [
                  const Color(0xFF2D3250), // Dark blue
                  const Color(0xFF1A1F35), // Darker blue
                ],
        ),
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: isSentByBot
                ? const Color(0xFF9C27B0).withOpacity(0.3) // Purple glow
                : const Color(0xFF3F51B5).withOpacity(0.3), // Indigo glow
            blurRadius: 12,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: isSentByBot
              ? const Color(0xFFBA68C8).withOpacity(0.5) // Light purple
              : const Color(0xFF7986CB).withOpacity(0.5), // Light indigo
          width: 1.0,
        ),
      ),
      child: Text(
        message,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          shadows: [
            Shadow(
              color: Color(0xFF9C27B0), // Purple shadow
              blurRadius: 4,
              offset: Offset(0, 1),
            ),
          ],
        ),
      ),
    );
  }
}

class WavePainter extends CustomPainter {
  final double waveAnimationValue;
  final double starsAnimationValue;
  final bool isRecording;

  WavePainter({
    required this.waveAnimationValue,
    required this.starsAnimationValue,
    required this.isRecording,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF0B0F2E).withOpacity(0.4) // Darker blue color
      ..style = PaintingStyle.fill;

    final path = Path();
    final width = size.width;
    final height = size.height;
    const waveHeight = 50.0;
    const waveCount = 3;

    // Create a darker background overlay
    final backgroundPaint = Paint()
      ..color = const Color(0xFF050A1A) // Very dark blue/black
      ..style = PaintingStyle.fill;

    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), backgroundPaint);

    // Draw nebula-like effects
    final nebulaPaint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 50);

    // Draw a few nebula clouds
    for (int i = 0; i < 3; i++) {
      final nebulaPath = Path();
      final centerX = width * (0.2 + i * 0.3);
      final centerY = height * 0.3;
      final radius = width * 0.15;

      nebulaPath.addOval(Rect.fromCircle(
        center: Offset(centerX, centerY),
        radius: radius,
      ));

      // Use different colors for each nebula
      switch (i) {
        case 0:
          nebulaPaint.color =
              const Color(0xFF0A1128).withOpacity(0.3); // Very dark blue
          break;
        case 1:
          nebulaPaint.color =
              const Color(0xFF1C2541).withOpacity(0.2); // Dark blue-gray
          break;
        default:
          nebulaPaint.color =
              const Color(0xFF0B3954).withOpacity(0.15); // Dark teal-blue
      }

      canvas.drawPath(nebulaPath, nebulaPaint);
    }

    // Draw 5-pointed stars in the background
    // Only animate stars if recording
    if (isRecording) {
      _drawStars(canvas, size, starsAnimationValue);
    } else {
      _drawStars(canvas, size, 0); // Static stars when not recording
    }

    // Draw waves
    for (int i = 0; i < waveCount; i++) {
      path.reset();
      final phase = waveAnimationValue * (i + 1) * 2;
      path.moveTo(0, height / 2 + waveHeight * sin(phase));
      for (double x = 0; x <= width; x++) {
        path.lineTo(x, height / 2 + waveHeight * sin(x * 0.02 + phase));
      }
      path.lineTo(width, height);
      path.lineTo(0, height);

      // Use different colors for each wave - darker shades
      Color waveColor;
      switch (i) {
        case 0:
          waveColor = const Color(0xFF1A237E).withOpacity(0.4); // Dark indigo
          break;
        case 1:
          waveColor = const Color(0xFF0D47A1).withOpacity(0.3); // Dark blue
          break;
        default:
          waveColor =
              const Color(0xFF0B0F2E).withOpacity(0.2); // Very dark blue
      }

      canvas.drawPath(path, paint..color = waveColor);
    }
  }

  void _drawStars(Canvas canvas, Size size, double animationValue) {
    final random = Random(42); // Fixed seed for consistent star positions
    final starsList = List.generate(
      25, // Reduced number of stars
      (index) => _Star(
        x: random.nextDouble() * size.width,
        y: random.nextDouble() * size.height * 0.6, // Keep stars in upper part
        size: random.nextDouble() * 1.5 + 0.8, // Slightly larger stars
        brightness: random.nextDouble() * 0.5 + 0.5, // Brighter stars
      ),
    );

    for (final star in starsList) {
      // Apply very slow movement only when recording
      final x = star.x + (animationValue * 5) % (size.width * 0.05);

      _drawDetailedStar(
        canvas,
        Offset(x, star.y),
        star.size,
        star.brightness,
      );
    }
  }

  void _drawDetailedStar(
      Canvas canvas, Offset center, double size, double brightness) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(brightness)
      ..style = PaintingStyle.fill;

    // Create a path for a 5-pointed star
    final path = Path();
    final outerRadius = size * 1.5;
    final innerRadius = size * 0.6;

    for (int i = 0; i < 5; i++) {
      final outerAngle = 2 * pi * i / 5 - pi / 2;
      final innerAngle = 2 * pi * (i + 0.5) / 5 - pi / 2;

      final outerPoint = Offset(
        center.dx + cos(outerAngle) * outerRadius,
        center.dy + sin(outerAngle) * outerRadius,
      );

      final innerPoint = Offset(
        center.dx + cos(innerAngle) * innerRadius,
        center.dy + sin(innerAngle) * innerRadius,
      );

      if (i == 0) {
        path.moveTo(outerPoint.dx, outerPoint.dy);
      } else {
        path.lineTo(outerPoint.dx, outerPoint.dy);
      }

      path.lineTo(innerPoint.dx, innerPoint.dy);
    }

    path.close();

    // Add subtle glow effect
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, size * 0.8);
    canvas.drawPath(path, paint);

    // Draw the core of the star
    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant WavePainter oldDelegate) {
    return oldDelegate.waveAnimationValue != waveAnimationValue ||
        oldDelegate.starsAnimationValue != starsAnimationValue ||
        oldDelegate.isRecording != isRecording;
  }
}

class _Star {
  final double x;
  final double y;
  final double size;
  final double brightness;

  _Star({
    required this.x,
    required this.y,
    required this.size,
    required this.brightness,
  });
}
