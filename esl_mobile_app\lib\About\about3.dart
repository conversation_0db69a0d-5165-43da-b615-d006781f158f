import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart'; // Add this import
import 'package:my_esl_app/constants/app_colors.dart'; // Import colors.dart
//import '../utils/theme_constants.dart';

class About3Page extends StatelessWidget {
  const About3Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color.fromARGB(255, 134, 217, 209), // Lighter teal
                  Color.fromARGB(255, 24, 195, 178), // Medium teal
                  Color.fromARGB(255, 3, 127, 106), // Darker teal
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Stack for the three images
              SizedBox(
                height: 300,
                width: 400,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Left image
                    Positioned(
                      left: 0,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          image: const DecorationImage(
                            image: AssetImage('assets/images/about_2.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Right image
                    Positioned(
                      right: 0,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          image: const DecorationImage(
                            image: AssetImage('assets/about_1.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Center image
                    Positioned(
                      child: Container(
                        width: 300,
                        height: 300,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          image: const DecorationImage(
                            image: AssetImage('assets/about_3.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              // Text with Nunito font
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  'Master English Smartly with\nAI-Personalized Learning!',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.rubik(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}