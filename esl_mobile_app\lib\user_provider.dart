import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:convert';

class UserProvider with ChangeNotifier {
  String? _userId;
  String? _phoneNumber;
  String? _firstName;
  String? _lastName;
  String? _email;
  String? _nationality;
  String? _nativeLanguage;
  String? _levelOfEnglish;
  String? _userName;
  int? _age;
  String? _profilePicture; // Path to local image file or base64 string
  String? _profession; // For users 18+
  String? _schoolClass; // For users under 18
  String? _reasonForLearning; // Why the user wants to learn English

  // Getters
  String? get userId => _userId;
  String? get phoneNumber => _phoneNumber;
  String? get firstName => _firstName;
  String? get lastName => _lastName;
  String? get email => _email;
  String? get nationality => _nationality;
  String? get nativeLanguage => _nativeLanguage;
  String? get levelOfEnglish => _levelOfEnglish;
  String? get userName => _userName;
  int? get age => _age;
  String? get profilePicture => _profilePicture;
  String? get profession => _profession;
  String? get schoolClass => _schoolClass;
  String? get reasonForLearning => _reasonForLearning;

  // Clear all user details
  void clearUserDetails() {
    _userId = null;
    _phoneNumber = null;
    _firstName = null;
    _lastName = null;
    _email = null;
    _nationality = null;
    _nativeLanguage = null;
    _levelOfEnglish = null;
    _userName = null;
    _age = null;
    _profilePicture = null;
    _profession = null;
    _schoolClass = null;
    _reasonForLearning = null;
    notifyListeners();
  }

  // Save profile image to local storage
  Future<void> saveProfileImage(String? base64Image) async {
    if (base64Image == null) {
      _profilePicture = null;
      notifyListeners();
      return;
    }
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/profile_image.png';
      final file = File(filePath);
      final bytes = base64Decode(base64Image);
      await file.writeAsBytes(bytes);
      _profilePicture = filePath;
      notifyListeners();
    } catch (e) {
      print('Error saving profile image: $e');
    }
  }

  // Setter method to update user details
  Future<void> setUserDetails({
    String? userId,
    String? phoneNumber,
    String? firstName,
    String? lastName,
    String? email,
    String? nationality,
    String? nativeLanguage,
    String? levelOfEnglish,
    String? userName,
    int? age,
    String? profilePicture, // Base64 string
    String? profession,
    String? schoolClass,
    String? reasonForLearning,
  }) async {
    _userId = userId;
    _phoneNumber = phoneNumber;
    _firstName = firstName;
    _lastName = lastName;
    _email = email;
    _nationality = nationality;
    _nativeLanguage = nativeLanguage;
    _levelOfEnglish = levelOfEnglish;
    _userName = userName;
    _age = age;
    _profession = profession;
    _schoolClass = schoolClass;
    _reasonForLearning = reasonForLearning;
    await saveProfileImage(profilePicture);
    notifyListeners();
  }
}
