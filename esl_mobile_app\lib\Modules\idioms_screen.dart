import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_html/flutter_html.dart';
import 'package:confetti/confetti.dart';
import 'package:audioplayers/audioplayers.dart';

class QuizQuestion {
  final int questionId;
  final String question;
  final List<String> options;
  final String correctOption;
  final String definition;

  QuizQuestion({
    required this.questionId,
    required this.question,
    required this.options,
    required this.correctOption,
    required this.definition,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      questionId: json['question_id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      correctOption: json['correct_option'],
      definition: json['definition'],
    );
  }
}

class IdiomsPage extends StatefulWidget {
  const IdiomsPage({Key? key}) : super(key: key);

  @override
  _IdiomsPageState createState() => _IdiomsPageState();
}

class _IdiomsPageState extends State<IdiomsPage>
    with SingleTickerProviderStateMixin {
  int _currentQuestionIndex = 0;
  int _score = 0;
  List<QuizQuestion> _questions = [];
  List<QuizQuestion> _quizQuestions = [];
  bool _showQuiz = false;
  bool _showRobotPopup = false;
  bool _isCorrect = false;
  String? _selectedOption;
  QuizQuestion? _questionData;
  bool _showAnswerFeedback = false;
  bool _quizFinished = false;
  bool _isAnswerChecked = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late ConfettiController _confettiController;
  double _scoreOpacity = 0.0;

  final List<Color> _pastelColors = [
    Color(0xFFFFF9C4), // Light Yellow
    Color(0xFFFFE0B2), // Light Orange
    Color(0xFFB2EBF2), // Light Cyan
    Color(0xFFD1C4E9), // Light Purple
    Color(0xFFC8E6C9), // Light Green
    Color(0xFFFFCDD2), // Light Pink
    Color(0xFFFFF8E1), // Light Amber
    Color(0xFFE1BEE7), // Light Magenta
    Color(0xFFDCEDC8), // Light Lime
    Color(0xFFBBDEFB), // Light Blue
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));
    _loadQuestions().then((_) => _startQuiz());
  }

  @override
  void dispose() {
    _animationController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  Future<void> _loadQuestions() async {
    try {
      String jsonString = await rootBundle.loadString('assets/questions.json');
      List<dynamic> data = jsonDecode(jsonString);
      setState(() {
        _questions = data.map((json) => QuizQuestion.fromJson(json)).toList();
      });
    } catch (e) {
      print('Error loading questions: $e');
    }
  }

  void _startQuiz() {
    List<QuizQuestion> shuffledQuestions = List.from(_questions)..shuffle();
    setState(() {
      _quizQuestions = shuffledQuestions.sublist(0, 10);
      _currentQuestionIndex = 0;
      _score = 0;
      _showQuiz = true;
      _selectedOption = null;
      _questionData = _quizQuestions[0];
      _showAnswerFeedback = false;
      _quizFinished = false;
      _isAnswerChecked = false;
      _scoreOpacity = 0.0;
    });
  }

  void _checkAnswer() {
    if (_selectedOption == null) return;
    bool isAnswerCorrect = _selectedOption == _questionData!.correctOption;
    setState(() {
      _isCorrect = isAnswerCorrect;
      if (isAnswerCorrect) {
        _score++;
        _playCorrectSound();
      } else {
        _playWrongSound();
      }
      _showRobotPopup = true;
      _showAnswerFeedback = true;
      _isAnswerChecked = true;
      _animationController.forward();
    });
  }

  void _nextQuestion() {
    if (!_isAnswerChecked) {
      _checkAnswer();
    } else {
      setState(() {
        _showRobotPopup = false;
        _showAnswerFeedback = false;
        _selectedOption = null;
        _isAnswerChecked = false;
        _animationController.reset();
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          _currentQuestionIndex++;
          _questionData = _quizQuestions[_currentQuestionIndex];
        } else {
          _showQuiz = false;
          _quizFinished = true;
          _confettiController.play();
          _playApplause();
          Future.delayed(const Duration(seconds: 2), () {
            setState(() {
              _scoreOpacity = 1.0;
            });
          });
        }
      });
    }
  }

  void _playApplause() async {
    final player = AudioPlayer();
    await player.play(AssetSource('sounds/applause-180037.mp3'));
    await Future.delayed(Duration(seconds: 2));
    await player.stop();
  }

  void _playCorrectSound() async {
    final player = AudioPlayer();
    await player.play(AssetSource('sounds/correct_answer.mp3'));
    await Future.delayed(Duration(seconds: 1));
    await player.stop();
  }

  void _playWrongSound() async {
    final player = AudioPlayer();
    await player.play(AssetSource('sounds/wrong_answer.mp3'));
    await Future.delayed(Duration(seconds: 1));
    await player.stop();
  }

  void _goBack() {
    Navigator.pop(context);
  }

  String _getMotivationalMessage(int score) {
    List<String> lowScoreMessages = [
      "Don’t worry, you’ll get better! 💪",
      "Keep going, success is near! 🚀",
      "Practice makes perfect! 🧠",
      "Stay motivated and try again! 🌟"
    ];

    List<String> mediumScoreMessages = [
      "You're improving! 👍",
      "Good job! Keep it up! ✨",
      "You're getting there! 🔥",
      "Nice effort, you’re on the right track! 🎯"
    ];

    List<String> highScoreMessages = [
      "Excellent work! 🏆",
      "You're an idioms expert! 🤓",
      "Awesome job! 🎉",
      "You nailed it! 👏"
    ];

    if (score < 5) {
      return lowScoreMessages[Random().nextInt(lowScoreMessages.length)];
    } else if (score < 8) {
      return mediumScoreMessages[Random().nextInt(mediumScoreMessages.length)];
    } else {
      return highScoreMessages[Random().nextInt(highScoreMessages.length)];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(color: const Color(0xFFEDE7F6)),
          Padding(
            padding: const EdgeInsets.all(42.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 0),
                  if (_showQuiz && _questionData != null)
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(1, 0),
                            end: Offset.zero,
                          ).animate(animation),
                          child: child,
                        );
                      },
                      child: Card(
                        key: ValueKey<int>(_currentQuestionIndex),
                        elevation: 8,
                        color: _pastelColors[
                            _currentQuestionIndex % _pastelColors.length],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: const EdgeInsets.symmetric(vertical: 10),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Html(
                                data:
                                    '<b>${_currentQuestionIndex + 1}. ${_questionData!.question}</b>',
                                style: {
                                  "body": Style(
                                    fontSize: FontSize(20.0),
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                },
                              ),
                              const SizedBox(height: 20),
                              Center(
                                child: ConstrainedBox(
                                  constraints:
                                      const BoxConstraints(maxWidth: 400),
                                  child: Column(
                                    children:
                                        _questionData!.options.map((option) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 6.0),
                                        child: ElevatedButton(
                                          onPressed: () {
                                            setState(() {
                                              _selectedOption = option;
                                            });
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: _showAnswerFeedback
                                                ? (option ==
                                                        _questionData!
                                                            .correctOption
                                                    ? Colors.green
                                                    : (_selectedOption == option
                                                        ? Colors.red
                                                        : const Color(
                                                            0xFFB2DFDB)))
                                                : (_selectedOption == option
                                                    ? const Color(0xFF00796B)
                                                    : const Color(0xFFB2DFDB)),
                                            foregroundColor: _showAnswerFeedback
                                                ? Colors.white
                                                : (_selectedOption == option
                                                    ? Colors.white
                                                    : Colors.black87),
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 16.0,
                                              horizontal: 24.0,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            textStyle:
                                                const TextStyle(fontSize: 16),
                                          ),
                                          child: Text(option),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 20),
                              if (_showRobotPopup)
                                ScaleTransition(
                                  scale: _scaleAnimation,
                                  child: Center(
                                    child: Container(
                                      padding: const EdgeInsets.all(16.0),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0.5),
                                            spreadRadius: 5,
                                            blurRadius: 7,
                                            offset: const Offset(0, 3),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Text(
                                            'Meaning',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(_questionData!.definition),
                                          const SizedBox(height: 10),
                                          Icon(
                                            _isCorrect
                                                ? Icons.thumb_up
                                                : Icons.thumb_down,
                                            size: 50,
                                            color: _isCorrect
                                                ? Colors.green
                                                : Colors.red,
                                          ),
                                          Text(_isCorrect
                                              ? 'Correct!'
                                              : 'Incorrect!'),
                                          if (!_isCorrect)
                                            Text(
                                              'The correct answer is ${_questionData!.correctOption}',
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              const SizedBox(height: 20),
                              Center(
                                child: ElevatedButton(
                                  onPressed: _selectedOption != null ||
                                          _isAnswerChecked
                                      ? _nextQuestion
                                      : null,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.teal,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: Text(
                                    _isAnswerChecked &&
                                            _currentQuestionIndex <
                                                _quizQuestions.length - 1
                                        ? 'Next'
                                        : _isAnswerChecked
                                            ? 'Finish'
                                            : 'Next',
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  if (!_showQuiz && _quizFinished)
                    AnimatedOpacity(
                      opacity: _scoreOpacity,
                      duration: const Duration(seconds: 2),
                      child: Column(
                        children: [
                          const SizedBox(height: 20),
                          Card(
                            color: Colors.white,
                            elevation: 6,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                children: [
                                  Text(
                                    'Your Score\n$_score / ${_quizQuestions.length}',
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.teal,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const Text(
                                    'Feedback 🎯',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    _getMotivationalMessage(_score),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.black87,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'Thank you for playing!',
                            style:
                                TextStyle(fontSize: 18, color: Colors.black87),
                          ),
                          ElevatedButton(
                            onPressed: _goBack,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.teal,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Back'),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          if (_quizFinished)
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.directional,
                blastDirection: pi / 2,
                numberOfParticles: 50,
                minBlastForce: 10,
                maxBlastForce: 30,
                gravity: 0.1,
                shouldLoop: false,
                colors: const [
                  Colors.green,
                  Colors.blue,
                  Colors.pink,
                  Colors.orange,
                  Colors.purple,
                ],
              ),
            ),
        ],
      ),
    );
  }
}
