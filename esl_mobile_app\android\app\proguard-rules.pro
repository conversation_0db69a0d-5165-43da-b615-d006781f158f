# Keep Razorpay classes and dependencies
-keep class com.razorpay.** { *; }
-dontwarn com.razorpay.**

# Keep Google Pay (NB Payments) classes referenced by Razorpay
-keep class com.google.android.apps.nbu.paisa.inapp.client.api.** { *; }
-dontwarn com.google.android.apps.nbu.paisa.inapp.client.api.**

# Keep ProGuard annotations used by Razorpay
-keep class proguard.annotation.** { *; }
-dontwarn proguard.annotation.**

# Suppress warnings for mic_stream plugin (optional, based on earlier warnings)
-dontwarn com.code.aaron.micstream.**