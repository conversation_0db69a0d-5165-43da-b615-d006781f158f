import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

const openAiApiKey = 'YOUR_OPENAI_API_KEY'; // Replace with your real key

Future<List<Map<String, dynamic>>> fetchGameData() async {
  const prompt = '''
Give me 4 English vocabulary words. For each word, return:
- Word
- Meaning
- Emoji story (max 3 emojis)
- Funny or simple example sentence

Respond in JSON list format like:
[
  {
    "word": "Quaff",
    "meaning": "To drink quickly or in large amounts",
    "emoji": "🍺😋💦",
    "example": "He quaffed the lemonade after the race."
  },
  ...
]
''';

  final response = await http.post(
    Uri.parse('https://api.openai.com/v1/chat/completions'),
    headers: {
      'Authorization': 'Bearer $openAiApiKey',
      'Content-Type': 'application/json',
    },
    body: jsonEncode({
      "model": "gpt-4",
      "messages": [
        {"role": "user", "content": prompt}
      ],
      "temperature": 0.8,
    }),
  );

  if (response.statusCode == 200) {
    final content =
        jsonDecode(response.body)["choices"][0]["message"]["content"];
    return List<Map<String, dynamic>>.from(jsonDecode(content));
  } else {
    throw Exception("Failed to fetch data from OpenAI");
  }
}

class EmojiStoryCard extends StatelessWidget {
  final String word;
  final String emojiStory;
  final String exampleSentence;

  const EmojiStoryCard({
    required this.word,
    required this.emojiStory,
    required this.exampleSentence,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Emoji Story:",
                style: Theme.of(context).textTheme.titleMedium),
            Text(emojiStory, style: const TextStyle(fontSize: 32)),
            const SizedBox(height: 12),
            Text("Word: $word", style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 8),
            Text("Example: $exampleSentence",
                style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}

class MemoryMatchGame extends StatelessWidget {
  final List<Map<String, String>> wordPairs;

  const MemoryMatchGame({super.key, required this.wordPairs});

  @override
  Widget build(BuildContext context) {
    final items = wordPairs
        .expand((pair) => [pair['word']!, pair['meaning']!])
        .toList()
      ..shuffle();

    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;

        return GridView.builder(
          itemCount: items.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 2.2,
          ),
          itemBuilder: (context, index) {
            return Card(
              elevation: 3,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    items[index],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class GamifiedDailyActivitiesPage extends StatefulWidget {
  const GamifiedDailyActivitiesPage({super.key});

  @override
  State<GamifiedDailyActivitiesPage> createState() =>
      _GamifiedDailyActivitiesPageState();
}

class _GamifiedDailyActivitiesPageState
    extends State<GamifiedDailyActivitiesPage> {
  List<Map<String, dynamic>>? gameData;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadGameData();
  }

  Future<void> loadGameData() async {
    try {
      final data = await fetchGameData();
      setState(() {
        gameData = data;
        isLoading = false;
      });
    } catch (e) {
      debugPrint("Error loading game data: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading || gameData == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          EmojiStoryCard(
            word: gameData![0]['word'],
            emojiStory: gameData![0]['emoji'],
            exampleSentence: gameData![0]['example'],
          ),
          const SizedBox(height: 16),
          Text(
            "Match the Word and Meaning",
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 12),
          MemoryMatchGame(
            wordPairs: gameData!
                .map((e) => {
                      'word': e['word'] as String,
                      'meaning': e['meaning'] as String,
                    })
                .toList(),
          ),
        ],
      ),
    );
  }
}

// HOME PAGE
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("ESL Home")),
      body: const SafeArea(
        child: GamifiedDailyActivitiesPage(),
      ),
    );
  }
}

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Emoji Vocabulary Game',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.pink,
        scaffoldBackgroundColor: Colors.white,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const HomePage(),
    );
  }
}
