import 'package:flutter/material.dart';

class WaveRecordingAnimation extends StatelessWidget {
  final double level;

  const WaveRecordingAnimation({super.key, required this.level});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 60,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _buildBar(0.3, level),
            _buildBar(0.5, level),
            _buildBar(0.7, level),
            _buildBar(0.9, level),
            _buildBar(0.7, level),
            _buildBar(0.5, level),
            _buildBar(0.3, level),
          ],
        ),
      ),
    );
  }

  Widget _buildBar(double baseHeight, double level) {
    final height = baseHeight * level * 60;
    return Container(
      width: 8,
      height: height,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
