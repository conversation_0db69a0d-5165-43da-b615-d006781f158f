import 'package:flutter/material.dart';
import 'package:fluid_bottom_nav_bar/fluid_bottom_nav_bar.dart';

class Footer extends StatefulWidget {
  final Function(int) onTabSelected;

  const Footer({
    super.key,
    required this.onTabSelected,
  });

  @override
  State<Footer> createState() => _FooterState();
}

class _FooterState extends State<Footer> {
  Color _getDynamicBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.white
        : const Color.fromARGB(255, 6, 6, 6);
  }

  Color _getDynamicIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.white
        : const Color.fromARGB(255, 237, 240, 239);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // Neon glow effect at the bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 4, // Slightly thicker for visibility
          child: Container(
            color: Colors.teal, // Solid teal for glow
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.teal.withOpacity(isDarkMode ? 0.6 : 0.4),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        ),
        // Main navigation bar
        FluidNavBar(
          icons: [
            FluidNavBarIcon(
              icon: Icons.home,
              backgroundColor: Colors.teal,
              extras: {"label": "Home"},
            ),
            FluidNavBarIcon(
              icon: Icons.explore,
              backgroundColor: Colors.teal,
              extras: {"label": "Explore"},
            ),
            FluidNavBarIcon(
              icon: Icons.chat,
              backgroundColor: Colors.teal,
              extras: {"label": "Scenario"},
            ),
            FluidNavBarIcon(
              icon: Icons.person,
              backgroundColor: Colors.teal,
              extras: {"label": "Role Module"},
            ),
          ],
          onChange: widget.onTabSelected,
          style: FluidNavBarStyle(
            iconUnselectedForegroundColor: _getDynamicIconColor(context)
                .withOpacity(0.7), // White in light, teal in dark
            iconSelectedForegroundColor:
                _getDynamicIconColor(context), // White in light, teal in dark
            barBackgroundColor: _getDynamicBackgroundColor(
                context), // White in light, black in dark
          ),
          scaleFactor: 5.0,
          animationFactor: 2, // Slowed down animation
        ),
      ],
    );
  }
}
