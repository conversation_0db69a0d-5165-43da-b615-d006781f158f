// [Imports remain unchanged]
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:provider/provider.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:o3d/o3d.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:my_esl_app/user_provider.dart';
import 'package:my_esl_app/Modules/avatar_screen.dart';
import 'dart:math' as math;
import 'spin_wheel_animation.dart';

const String apiBaseUrl = 'http://talktoai.in:4003/';
const String openAiApiKey =
    '********************************************************************************************************************************************************************';

class ChatScreen extends StatefulWidget {
  final String? reason;
  final String? theme;
  final String? themeTopic;

  const ChatScreen({super.key, this.reason, this.theme, this.themeTopic});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  String? sessionId;
  bool _isLoading = false;
  bool _isRecording = false;
  bool _isAudioPlaying = false;
  bool _isConversationEnded = false;

  final List<Map<String, dynamic>> conversationHistory = [];
  final List<Map<String, dynamic>> _audioQueue = [];

  final AudioRecorder _audioRecorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ScrollController _scrollController = ScrollController();

  final O3DController _avatarController = O3DController();
  static const String _modelPath = 'assets/models/esl_ava_6casual.glb';

  late AnimationController _blinkController;
  late Animation<double> _blinkAnimation;
  late Animation<double> _scaleAnimation;
  late AnimationController _gradientController;
  late Animation<Color?> _colorAnimation1;
  late Animation<Color?> _colorAnimation2;
  late Animation<Color?> _colorAnimation3;

  final Map<String, Map<String, String>> scenarioDetails = {
    'Interview': {
      'reason': 'Job Interview',
      'theme': 'Professional',
      'themeTopic': 'Discussing qualifications and experience',
    },
    'Business Meeting': {
      'reason': 'Professional Development',
      'theme': 'Business',
      'themeTopic': 'Discussing project updates',
    },
    'Hotel Booking': {
      'reason': 'Travel',
      'theme': 'Hospitality',
      'themeTopic': 'Making a reservation',
    },
    'Restaurant Reservation': {
      'reason': 'Dining Experience',
      'theme': 'Hospitality',
      'themeTopic': 'Making a dinner reservation',
    },
    'Ordering Food': {
      'reason': 'Dining Experience',
      'theme': 'Hospitality',
      'themeTopic': 'Ordering food and drinks',
    },
    'Asking Directions': {
      'reason': 'Navigation',
      'theme': 'Travel',
      'themeTopic': 'Asking for and giving directions',
    },
    'Doctor Appointment': {
      'reason': 'Healthcare',
      'theme': 'Medical',
      'themeTopic': 'Booking a doctor appointment',
    },
    'Shopping': {
      'reason': 'Shopping',
      'theme': 'Retail',
      'themeTopic': 'Asking about sizes and prices',
    },
    'Airport Check-In': {
      'reason': 'Travel',
      'theme': 'Aviation',
      'themeTopic': 'Checking in for a flight',
    },
    'Attending Party': {
      'reason': 'Social Event',
      'theme': 'Socializing',
      'themeTopic': 'Conversing at a party',
    },
    'Visiting Museum': {
      'reason': 'Cultural Exploration',
      'theme': 'Tourism',
      'themeTopic': 'Asking about exhibits',
    },
    'Renting Car': {
      'reason': 'Travel',
      'theme': 'Transportation',
      'themeTopic': 'Renting a vehicle',
    },
  };

  final Map<String, String> scenarioImageUrls = {
    'Interview':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/interview.jpg',
    'Business Meeting':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/business_meeting.jpg',
    'Hotel Booking':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/hotel_booking.jpg',
    'Restaurant Reservation':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/restaurant_reservation.jpg',
    'Ordering Food':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/ordering_food.jpg',
    'Asking Directions':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/asking_direction.jpg',
    'Doctor Appointment':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/doctor_appointment.jpg',
    'Shopping':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/shopping_for_clothes.jpg',
    'Airport Check-In':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/airport_checkin.jpg',
    'Attending Party':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/attending_party.jpg',
    'Visiting Museum':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/visiting_museum.jpg',
    'Renting Car':
        'https://frontend-sasthra-demo-images-dsais.s3.eu-north-1.amazonaws.com/esl_roleplay/renting_car.jpg',
  };

  @override
  void initState() {
    super.initState();
    _setupAudioPlayer();
    _loadAvatar();

    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat(reverse: true);
    _blinkAnimation =
        Tween<double>(begin: 0.5, end: 1.0).animate(_blinkController);
    _scaleAnimation =
        Tween<double>(begin: 0.8, end: 1.0).animate(_blinkController);

    _gradientController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..repeat(reverse: true);
    _colorAnimation1 = ColorTween(
      begin: const Color(0xFF1A1F35),
      end: const Color(0xFF2D3250),
    ).animate(_gradientController);
    _colorAnimation2 = ColorTween(
      begin: const Color(0xFF2D3250),
      end: const Color(0xFF3F51B5),
    ).animate(_gradientController);
    _colorAnimation3 = ColorTween(
      begin: const Color(0xFF3F51B5),
      end: const Color(0xFF1A1F35),
    ).animate(_gradientController);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.reason != null &&
          widget.theme != null &&
          widget.themeTopic != null) {
        _startConversation(widget.reason!, widget.theme!, widget.themeTopic!);
      } else if (sessionId == null && conversationHistory.isEmpty) {
        _showThemeSelectionDialog();
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _audioRecorder.dispose();
    _blinkController.dispose();
    _gradientController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupAudioPlayer() {
    _audioPlayer.onPlayerStateChanged.listen((state) {
      if (!mounted) return;
      setState(() => _isAudioPlaying = state == PlayerState.playing);
      if (state == PlayerState.playing) {
        _avatarController.animationName = 'Talking';
        _avatarController.play();
      } else {
        _avatarController.animationName = 'Standing_Idle';
        _avatarController.play();
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!mounted || !_isAudioPlaying) {
            _avatarController.pause();
          }
        });
      }
    });

    _audioPlayer.onPlayerComplete.listen((_) {
      if (!mounted) return;
      _playNextAudio();
    });
  }

  Future<void> _loadAvatar() async {
    _avatarController.cameraOrbit(0, 80, 100);
    _avatarController.cameraTarget(0, 1.2, 0);
    Future.delayed(const Duration(seconds: 1), () {
      _avatarController.animationName = 'Standing_Idle';
      _avatarController.play();
    });
  }

  Future<void> _startConversation(
      String reason, String theme, String themeTopic) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please log in again.')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      conversationHistory.clear();
      _audioQueue.clear();
      _isConversationEnded = false;
      sessionId = null;
    });

    try {
      final resp = await http
          .post(
            Uri.parse('${apiBaseUrl}api/start'),
            headers: {'Content-Type': 'application/json; charset=utf-8'},
            body: jsonEncode({
              'user_id': int.parse(userId),
              'reason_of_learning': reason,
              'theme': theme,
              'theme_topic': themeTopic,
            }),
          )
          .timeout(const Duration(seconds: 60));

      if (!mounted) return;
      if (resp.statusCode == 200) {
        final decodedBody = utf8.decode(resp.bodyBytes);
        _handleApiResponse(jsonDecode(decodedBody), isStart: true);
      } else {
        _handleError('Failed to start', resp);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Network error: $e')));
    }
  }

  Future<String?> _transcribeAudio(String audioPath) async {
    try {
      final url = Uri.parse('https://api.openai.com/v1/audio/transcriptions');
      final request = http.MultipartRequest('POST', url);
      request.headers['Authorization'] = 'Bearer $openAiApiKey';
      request.fields['model'] = 'whisper-1';
      request.files.add(await http.MultipartFile.fromPath('file', audioPath));

      final response =
          await request.send().timeout(const Duration(seconds: 30));
      if (response.statusCode == 200) {
        final responseData = await response.stream.bytesToString();
        final json = jsonDecode(responseData);
        return json['text'];
      } else {
        setState(() => _isLoading = false);
        return null;
      }
    } catch (e) {
      setState(() => _isLoading = false);
      return null;
    }
  }

  Future<void> _submitTextResponse(String userText) async {
    if (sessionId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Session lost')),
      );
      return;
    }
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please log in again.')),
      );
      return;
    }

    try {
      final resp = await http
          .post(
            Uri.parse('${apiBaseUrl}api/submit'),
            headers: {'Content-Type': 'application/json; charset=utf-8'},
            body: jsonEncode({
              'user_id': int.parse(userId),
              'session_id': sessionId,
              'user_text': userText,
            }),
          )
          .timeout(const Duration(seconds: 90));

      if (!mounted) return;
      if (resp.statusCode == 200) {
        final decodedBody = utf8.decode(resp.bodyBytes);
        _handleApiResponse(jsonDecode(decodedBody), isStart: false);
      } else {
        _handleError('Submit failed', resp);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Network error: $e')));
    }
  }

  void _handleApiResponse(Map<String, dynamic> data, {required bool isStart}) {
    if (isStart && data['session_id'] != null) {
      sessionId = data['session_id'];
    }

    final texts = <Map<String, dynamic>>[];
    final audios = <Map<String, dynamic>>[];

    if (isStart) {
      if (data['introduction'] is String && data['introduction'].isNotEmpty) {
        texts.add({
          'sender': 'ai',
          'type': 'introduction',
          'content': data['introduction'],
        });
        final introAudio = data['audio_blobs']?['introduction_audio'];
        if (introAudio is String && introAudio.isNotEmpty) {
          audios
              .add({'type': 'introduction', 'audio': base64Decode(introAudio)});
        }
      }
      if (data['question'] is String && data['question'].isNotEmpty) {
        final questionGroup = {
          'sender': 'ai',
          'type': 'question_group',
          'question_content': data['question'],
        };
        if (data['hint'] is String && data['hint'].isNotEmpty) {
          questionGroup['hint_content'] = data['hint'];
          final hintAudio = data['audio_blobs']?['hint_audio'];
          if (hintAudio is String && hintAudio.isNotEmpty) {
            questionGroup['hint_audio'] = base64Decode(hintAudio);
          }
        }
        if (data['question_translation'] is String &&
            data['question_translation'].isNotEmpty) {
          questionGroup['translation_content'] = data['question_translation'];
          final transAudio = data['audio_blobs']?['question_translation_audio'];
          if (transAudio is String && transAudio.isNotEmpty) {
            questionGroup['translation_audio'] = base64Decode(transAudio);
          }
        }
        final questionAudio = data['audio_blobs']?['question_audio'];
        if (questionAudio is String && questionAudio.isNotEmpty) {
          questionGroup['question_audio'] = base64Decode(questionAudio);
          audios.add(
              {'type': 'question', 'audio': questionGroup['question_audio']});
        }
        texts.add(questionGroup);
      }
    } else {
      if (data['user_response'] is String) {
        texts.add({
          'sender': 'user',
          'type': 'response',
          'content': data['user_response'],
        });
      }
      if (data['feedback'] is String && data['feedback'].isNotEmpty) {
        texts.add({
          'sender': 'ai',
          'type': 'feedback',
          'content': data['feedback'],
        });
        final feedbackAudio = data['audio_blobs']?['feedback_audio'];
        if (feedbackAudio is String && feedbackAudio.isNotEmpty) {
          audios
              .add({'type': 'feedback', 'audio': base64Decode(feedbackAudio)});
        }
      }
      if (data['correction'] is String && data['correction'].isNotEmpty) {
        texts.add({
          'sender': 'ai',
          'type': 'correction',
          'content': data['correction'],
        });
        final correctionAudio = data['audio_blobs']?['correction_audio'];
        if (correctionAudio is String && correctionAudio.isNotEmpty) {
          audios.add(
              {'type': 'correction', 'audio': base64Decode(correctionAudio)});
        }
      }
      if (data['explanation'] is String && data['explanation'].isNotEmpty) {
        final explanationGroup = {
          'sender': 'ai',
          'type': 'explanation_group',
          'explanation_content': data['explanation'],
        };
        if (data['explanation_translation'] is String &&
            data['explanation_translation'].isNotEmpty) {
          explanationGroup['translation_content'] =
              data['explanation_translation'];
          final transAudio =
              data['audio_blobs']?['explanation_translation_audio'];
          if (transAudio is String && transAudio.isNotEmpty) {
            explanationGroup['translation_audio'] = base64Decode(transAudio);
          }
        }
        final explanationAudio = data['audio_blobs']?['explanation_audio'];
        if (explanationAudio is String && explanationAudio.isNotEmpty) {
          explanationGroup['explanation_audio'] =
              base64Decode(explanationAudio);
          audios.add({
            'type': 'explanation',
            'audio': explanationGroup['explanation_audio']
          });
        }
        texts.add(explanationGroup);
      }
      if (data['question'] is String && data['question'].isNotEmpty) {
        final questionGroup = {
          'sender': 'ai',
          'type': 'question_group',
          'question_content': data['question'],
        };
        if (data['hint'] is String && data['hint'].isNotEmpty) {
          questionGroup['hint_content'] = data['hint'];
          final hintAudio = data['audio_blobs']?['hint_audio'];
          if (hintAudio is String && hintAudio.isNotEmpty) {
            questionGroup['hint_audio'] = base64Decode(hintAudio);
          }
        }
        if (data['question_translation'] is String &&
            data['question_translation'].isNotEmpty) {
          questionGroup['translation_content'] = data['question_translation'];
          final transAudio = data['audio_blobs']?['question_translation_audio'];
          if (transAudio is String && transAudio.isNotEmpty) {
            questionGroup['translation_audio'] = base64Decode(transAudio);
          }
        }
        final questionAudio = data['audio_blobs']?['question_audio'];
        if (questionAudio is String && questionAudio.isNotEmpty) {
          questionGroup['question_audio'] = base64Decode(questionAudio);
          audios.add(
              {'type': 'question', 'audio': questionGroup['question_audio']});
        }
        texts.add(questionGroup);
      }
      if (data['final_feedback_display'] != null) {
        String? feedbackContent;
        if (data['final_feedback_display'] is Map) {
          final feedbackMap =
              data['final_feedback_display'] as Map<String, dynamic>;
          feedbackContent = feedbackMap.entries.map((e) {
            final section = e.key;
            final content = (e.value as List).join('\n');
            return '$section:\n$content';
          }).join('\n\n');
        } else if (data['final_feedback_display'] is String &&
            data['final_feedback_display'].isNotEmpty) {
          feedbackContent = data['final_feedback_display'];
        }
        if (feedbackContent != null) {
          texts.add({
            'sender': 'ai',
            'type': 'final_feedback',
            'content': feedbackContent,
          });
        }
        final finalFeedbackAudioBlob =
            data['audio_blobs']?['final_feedback_audio'];
        if (finalFeedbackAudioBlob is String &&
            finalFeedbackAudioBlob.isNotEmpty) {
          audios.add({
            'type': 'final_feedback',
            'audio': base64Decode(finalFeedbackAudioBlob)
          });
        }
      }
    }

    setState(() {
      _isLoading = false;
      _isRecording = false;
      _isConversationEnded = data['is_finished'] == true;
      conversationHistory.addAll(texts);
      _audioQueue.addAll(audios);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });

    if (_audioQueue.isNotEmpty && !_isAudioPlaying) {
      _playNextAudio();
    }
  }

  void _handleError(String ctx, http.Response resp) {
    setState(() => _isLoading = false);
    String msg;
    try {
      final decodedBody = utf8.decode(resp.bodyBytes);
      msg = jsonDecode(decodedBody)['detail'] ?? decodedBody;
    } catch (_) {
      msg = resp.body;
    }
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text('$ctx: $msg')));
  }

  Future<void> _playNextAudio() async {
    if (_audioQueue.isEmpty) return;
    final item = _audioQueue.removeAt(0);
    final bytes = item['audio'] as Uint8List;
    await _audioPlayer.play(BytesSource(bytes));
  }

  Future<void> _startRecording() async {
    final permissionStatus = await Permission.microphone.request();
    if (!permissionStatus.isGranted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Microphone permission denied')));
      }
      return;
    }

    await _audioPlayer.stop();
    final dir = await getTemporaryDirectory();
    final path = '${dir.path}/rec_${DateTime.now().millisecondsSinceEpoch}.m4a';
    try {
      await _audioRecorder.start(const RecordConfig(), path: path);
      if (mounted) {
        setState(() {
          _isRecording = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('Recording failed: $e')));
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;
    String? path;
    try {
      path = await _audioRecorder.stop();
      if (mounted) {
        setState(() {
          _isRecording = false;
        });
      }

      if (path != null && mounted) {
        setState(() => _isLoading = true);
        final transcribedText = await _transcribeAudio(path);

        if (transcribedText != null && mounted) {
          await _submitTextResponse(transcribedText);
        } else if (mounted) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Failed to transcribe audio. Please try again.')),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No audio recorded')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRecording = false;
          _isLoading = false;
        });
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (path != null) {
        final file = File(path);
        if (await file.exists()) await file.delete();
      }
    }
  }

  void _showThemeSelectionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => SpinWheelDialog(
        scenarios: scenarioDetails.keys.toList(),
        scenarioDetails: scenarioDetails,
        scenarioImageUrls: scenarioImageUrls,
        onScenarioSelected: (scenario) {
          Navigator.pop(context);
          _startConversation(
            scenarioDetails[scenario]!['reason']!,
            scenarioDetails[scenario]!['theme']!,
            scenarioDetails[scenario]!['themeTopic']!,
          );
        },
      ),
    );
  }

  bool get _canRecord =>
      sessionId != null &&
      !_isLoading &&
      !_isRecording &&
      !_isConversationEnded &&
      !_isAudioPlaying &&
      _audioQueue.isEmpty;

  void _showHint(String hint, Uint8List? audioBytes) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(204),
        title: const Text('Hint', style: TextStyle(color: Colors.white)),
        content: Text(hint, style: const TextStyle(color: Colors.white)),
        actions: [
          if (audioBytes != null)
            IconButton(
              icon: const Icon(Icons.volume_up, color: Colors.cyanAccent),
              onPressed: () async {
                await _audioPlayer.play(BytesSource(audioBytes));
              },
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Close', style: TextStyle(color: Colors.cyanAccent)),
          ),
        ],
      ),
    );
  }

  void _showTranslation(
      String translation, Uint8List? audioBytes, String title) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(204),
        title: Text(title, style: const TextStyle(color: Colors.white)),
        content: Text(translation, style: const TextStyle(color: Colors.white)),
        actions: [
          if (audioBytes != null)
            IconButton(
              icon: const Icon(Icons.volume_up, color: Colors.cyanAccent),
              onPressed: () async {
                await _audioPlayer.play(BytesSource(audioBytes));
              },
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Close', style: TextStyle(color: Colors.cyanAccent)),
          ),
        ],
      ),
    );
  }

  void _showExplanation(String explanation, Uint8List? audioBytes) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(204),
        title: const Text('Explanation', style: TextStyle(color: Colors.white)),
        content: Text(explanation, style: const TextStyle(color: Colors.white)),
        actions: [
          if (audioBytes != null)
            IconButton(
              icon: const Icon(Icons.volume_up, color: Colors.cyanAccent),
              onPressed: () async {
                await _audioPlayer.play(BytesSource(audioBytes));
              },
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Close', style: TextStyle(color: Colors.cyanAccent)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: Text('ENGLISH TRAINER',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black,
              fontWeight: FontWeight.bold,
              fontFamily: 'Nunito',
            )),
        backgroundColor: isDarkMode
            ? Colors.black
            : const Color.fromARGB(255, 255, 255, 255),
        centerTitle: true,
        leading: const Padding(
          padding: EdgeInsets.all(8),
        ),
        actions: [
          FadeTransition(
            opacity: _blinkAnimation,
            child: IconButton(
              icon: const Icon(Icons.settings,
                  color: Color.fromARGB(255, 11, 120, 139)),
              onPressed: () => Navigator.push(context,
                  MaterialPageRoute(builder: (_) => ThreeDModelPage())),
            ),
          ),
        ],
        elevation: 0,
      ),
      body: Stack(
        children: [
          isDarkMode
              ? Container(
                  color: Colors.black,
                )
              : Container(
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/day_sky.jpg'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
          Star(left: 20, top: 50, delay: Duration.zero),
          Star(left: 100, top: 150, delay: Duration(milliseconds: 300)),
          Star(left: 200, top: 250, delay: Duration(milliseconds: 600)),
          Star(left: 300, top: 350, delay: Duration(milliseconds: 900)),
          Star(left: 50, top: 400, delay: Duration(milliseconds: 1200)),
          Star(left: 150, top: 100, delay: Duration(milliseconds: 1500)),
          Star(left: 250, top: 200, delay: Duration(milliseconds: 1800)),
          Star(left: 350, top: 300, delay: Duration(milliseconds: 2100)),
          Star(left: 100, top: 400, delay: Duration(milliseconds: 2400)),
          Star(left: 200, top: 500, delay: Duration(milliseconds: 2700)),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    Colors.white.withAlpha(20),
                    Colors.transparent,
                  ],
                  center: Alignment.center,
                  radius: 1.5,
                ),
              ),
            ),
          ),
          Column(
            children: [
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 150),
                  itemCount: conversationHistory.length,
                  itemBuilder: (_, i) {
                    final m = conversationHistory[i];
                    final isUser = m['sender'] == 'user';
                    if (m['type'] == 'question_group') {
                      return Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 8, right: 60),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(230),
                            border: Border.all(
                                color: Colors.grey.shade300, width: 1.5),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 2,
                                  offset: Offset(0, 1))
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                m['question_content'],
                                style: GoogleFonts.notoSans(
                                    fontSize: 15, color: Colors.black87),
                              ),
                              if (m['hint_content'] != null ||
                                  m['translation_content'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (m['hint_content'] != null)
                                        GestureDetector(
                                          onTap: () {
                                            _showHint(m['hint_content'],
                                                m['hint_audio']);
                                          },
                                          child: Icon(Icons.lightbulb,
                                              color: Colors.yellowAccent,
                                              shadows: [
                                                Shadow(
                                                  color: Colors.yellowAccent,
                                                  blurRadius: 20,
                                                ),
                                              ]),
                                        ),
                                      if (m['translation_content'] != null)
                                        GestureDetector(
                                          onTap: () {
                                            _showTranslation(
                                                m['translation_content'],
                                                m['translation_audio'],
                                                'Question Translation');
                                          },
                                          child: Icon(Icons.translate,
                                              color: Colors.blueAccent,
                                              shadows: [
                                                Shadow(
                                                  color: Colors.blueAccent,
                                                  blurRadius: 20,
                                                ),
                                              ]),
                                        ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    } else if (m['type'] == 'explanation_group') {
                      return Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 8, right: 60),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(230),
                            border: Border.all(
                                color: Colors.grey.shade300, width: 1.5),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 2,
                                  offset: Offset(0, 1))
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                m['explanation_content'],
                                style: GoogleFonts.notoSans(
                                    fontSize: 15, color: Colors.black87),
                              ),
                              if (m['translation_content'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: GestureDetector(
                                    onTap: () {
                                      _showTranslation(
                                          m['translation_content'],
                                          m['translation_audio'],
                                          'Explanation Translation');
                                    },
                                    child: Icon(Icons.translate,
                                        color: Colors.blueAccent,
                                        shadows: [
                                          Shadow(
                                            color: Colors.blueAccent,
                                            blurRadius: 20,
                                          ),
                                        ]),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    } else if (m['type'] == 'final_feedback') {
                      return Card(
                        elevation: 4,
                        margin:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Final Feedback',
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold)),
                              SizedBox(height: 8),
                              Text(m['content'],
                                  style: TextStyle(fontSize: 16)),
                            ],
                          ),
                        ),
                      );
                    } else {
                      return Align(
                        alignment: isUser
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Container(
                          margin: EdgeInsets.only(
                            bottom: 8,
                            left: isUser ? 60 : 0,
                            right: isUser ? 0 : 60,
                          ),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isUser
                                ? Colors.teal[100]
                                : Colors.white.withAlpha(230),
                            border: isUser
                                ? null
                                : Border.all(
                                    color: Colors.grey.shade300, width: 1.5),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 2,
                                  offset: const Offset(0, 1))
                            ],
                          ),
                          child: Text(
                            isUser ? 'You: ${m['content']}' : m['content'],
                            style: GoogleFonts.notoSans(
                              fontSize: 15,
                              color: Colors.black87,
                            ),
                            textAlign:
                                isUser ? TextAlign.right : TextAlign.left,
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
              if (_isLoading || _isRecording || _isAudioPlaying)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 80),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(width: 50),
                      _isLoading
                          ? Image.asset(
                              'assets/thinking.png',
                              width: 60,
                              height: 60,
                            )
                          : Text(
                              _isRecording ? 'Recording...' : 'AI Speaking...',
                              style: TextStyle(
                                color: _isRecording
                                    ? Colors.redAccent
                                    : const Color.fromARGB(255, 15, 16, 16),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ],
                  ),
                ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 32, 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    FloatingActionButton(
                      backgroundColor: _isRecording
                          ? Colors.redAccent
                          : const Color.fromARGB(255, 240, 245, 244),
                      onPressed: _canRecord || _isRecording
                          ? () {
                              if (_isRecording) {
                                _stopRecording();
                              } else {
                                _startRecording();
                              }
                            }
                          : null,
                      child: FadeTransition(
                        opacity: _blinkAnimation,
                        child: Icon(
                          _isRecording ? Icons.stop : Icons.mic,
                          color: const Color.fromARGB(255, 12, 121, 121),
                          shadows: const [
                            Shadow(
                              color: Color.fromARGB(255, 15, 119, 119),
                              blurRadius: 10,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            bottom: -650,
            left: -135,
            width: 400,
            height: 1050,
            child: ClipRect(
              child: O3D.asset(
                src: _modelPath,
                controller: _avatarController,
                cameraControls: false,
                backgroundColor: Colors.transparent,
                ar: false,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SpinWheelDialog extends StatefulWidget {
  final List<String> scenarios;
  final Map<String, Map<String, String>> scenarioDetails;
  final Map<String, String> scenarioImageUrls;
  final Function(String) onScenarioSelected;

  const _SpinWheelDialog({
    required this.scenarios,
    required this.scenarioDetails,
    required this.scenarioImageUrls,
    required this.onScenarioSelected,
  });

  @override
  __SpinWheelDialogState createState() => __SpinWheelDialogState();
}

class __SpinWheelDialogState extends State<_SpinWheelDialog>
    with TickerProviderStateMixin {
  late AnimationController _spinController;
  late Animation<double> _spinAnimation;
  String? _selectedScenario;
  bool _isSpinning = true;

  @override
  void initState() {
    super.initState();
    _spinController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _isSpinning = false;
            final randomAngle = _spinAnimation.value % (2 * math.pi);
            final segmentAngle = 2 * math.pi / 12;
            final selectedIndex = ((randomAngle / segmentAngle) % 12).floor();
            _selectedScenario = widget.scenarios[selectedIndex];
          });
        }
      });

    final randomRotations = 5 + math.Random().nextDouble() * 5;
    _spinAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi * randomRotations,
    ).animate(
      CurvedAnimation(
        parent: _spinController,
        curve: Curves.easeOutCubic,
      ),
    );

    _spinController.forward();
  }

  @override
  void dispose() {
    _spinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.85,
          maxHeight: MediaQuery.of(context).size.height * 0.65,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(230),
            borderRadius: BorderRadius.circular(16),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text(
                    _isSpinning
                        ? 'Spinning the Wheel...'
                        : 'Scenario Selected!',
                    style: GoogleFonts.notoSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: _isSpinning
                      ? Stack(
                          alignment: Alignment.center,
                          children: [
                            AnimatedBuilder(
                              animation: _spinAnimation,
                              builder: (context, child) {
                                return Transform.rotate(
                                  angle: _spinAnimation.value,
                                  child: CustomPaint(
                                    size: Size(250, 250),
                                    painter: WheelPainter(
                                      scenarios: widget.scenarios,
                                      scenarioImageUrls:
                                          widget.scenarioImageUrls,
                                    ),
                                  ),
                                );
                              },
                            ),
                            Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.red,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black26,
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(Icons.arrow_drop_down,
                                  color: Colors.white, size: 28),
                            ),
                          ],
                        )
                      : _selectedScenario != null
                          ? _ScenarioCard(
                              scenario: _selectedScenario!,
                              imageUrl:
                                  widget.scenarioImageUrls[_selectedScenario]!,
                              onTap: () {},
                            )
                          : Container(),
                ),
                if (!_isSpinning)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(8, 8, 8, 12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _isSpinning = true;
                              _selectedScenario = null;
                            });
                            final randomRotations =
                                5 + math.Random().nextDouble() * 5;
                            _spinAnimation = Tween<double>(
                              begin: 0.0,
                              end: 2 * math.pi * randomRotations,
                            ).animate(
                              CurvedAnimation(
                                parent: _spinController,
                                curve: Curves.easeOutCubic,
                              ),
                            );
                            _spinController.reset();
                            _spinController.forward();
                          },
                          child: Text(
                            'Spin Again',
                            style: GoogleFonts.notoSans(
                              fontSize: 14,
                              color: Colors.teal,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            if (_selectedScenario != null) {
                              widget.onScenarioSelected(_selectedScenario!);
                            }
                          },
                          child: Text(
                            'Confirm',
                            style: GoogleFonts.notoSans(
                              fontSize: 14,
                              color: Colors.teal,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'Close',
                            style: GoogleFonts.notoSans(
                              fontSize: 14,
                              color: Colors.teal,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class WheelPainter extends CustomPainter {
  final List<String> scenarios;
  final Map<String, String> scenarioImageUrls;

  WheelPainter({required this.scenarios, required this.scenarioImageUrls});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    const int partitionCount = 12;
    final segmentAngle = 2 * math.pi / partitionCount;

    final displayScenarios = List<String>.filled(partitionCount, '');
    for (int i = 0; i < partitionCount; i++) {
      displayScenarios[i] = scenarios.length > i ? scenarios[i] : '';
    }

    for (int i = 0; i < partitionCount; i++) {
      final startAngle = i * segmentAngle;
      final sweepAngle = segmentAngle;
      final paint = Paint()
        ..color = Colors.teal.withOpacity(0.8)
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      if (displayScenarios[i].isNotEmpty) {
        final textSpan = TextSpan(
          text: displayScenarios[i],
          style: GoogleFonts.notoSans(
            fontSize: 7,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        );
        final textPainter = TextPainter(
          text: textSpan,
          textAlign: TextAlign.center,
          textDirection: TextDirection.ltr,
          maxLines: 2,
          ellipsis: '...',
        );
        textPainter.layout(maxWidth: radius * 0.5);

        final textAngle = startAngle + sweepAngle / 2;
        final textRadius = radius * 0.45;
        final textOffset = Offset(
          center.dx + textRadius * math.cos(textAngle),
          center.dy + textRadius * math.sin(textAngle),
        );

        canvas.save();
        canvas.translate(textOffset.dx, textOffset.dy);
        canvas.rotate(textAngle + math.pi / 2);
        textPainter.paint(
            canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
        canvas.restore();
      }
    }

    final borderPaint = Paint()
      ..color = Colors.black26
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    canvas.drawCircle(center, radius, borderPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

class _ScenarioCard extends StatefulWidget {
  final String scenario;
  final String imageUrl;
  final VoidCallback onTap;

  const _ScenarioCard({
    required this.scenario,
    required this.imageUrl,
    required this.onTap,
  });

  @override
  __ScenarioCardState createState() => __ScenarioCardState();
}

class __ScenarioCardState extends State<_ScenarioCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onTap();
      },
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.teal.withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.imageUrl,
                  width: 48,
                  height: 48,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.image_not_supported,
                      color: Colors.white,
                      size: 36,
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return SizedBox(
                      width: 48,
                      height: 48,
                      child: Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                    );
                  },
                ),
              ),
              SizedBox(height: 8),
              Text(
                widget.scenario,
                style: GoogleFonts.notoSans(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Star extends StatefulWidget {
  final double left;
  final double top;
  final Duration delay;

  const Star({
    Key? key,
    required this.left,
    required this.top,
    required this.delay,
  }) : super(key: key);

  @override
  State<Star> createState() => _StarState();
}

class _StarState extends State<Star> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late Color _starColor;

  @override
  void initState() {
    super.initState();
    final colors = [
      const Color(0xFF9C27B0),
      const Color(0xFF3F51B5),
      const Color(0xFF2196F3),
    ];
    _starColor = colors[DateTime.now().millisecond % colors.length];

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    Future.delayed(widget.delay, () {
      _controller.repeat(reverse: true);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.left,
      top: widget.top,
      child: FadeTransition(
        opacity: _animation,
        child: CustomPaint(
          size: const Size(10, 10),
          painter: StarPainter(color: _starColor),
        ),
      ),
    );
  }
}

class StarPainter extends CustomPainter {
  final Color color;

  StarPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double radius = size.width / 2;
    final double innerRadius = radius * 0.4;

    final Path path = Path();

    for (int i = 0; i < 5; i++) {
      final double outerAngle = (i * 2 * 3.14159 / 5) - 3.14159 / 2;
      final double outerX = centerX + radius * math.cos(outerAngle);
      final double outerY = centerY + radius * math.sin(outerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      final double innerAngle = ((i + 0.5) * 2 * 3.14159 / 5) - 3.14159 / 2;
      final double innerX = centerX + innerRadius * math.cos(innerAngle);
      final double innerY = centerY + innerRadius * math.sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();

    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, size.width * 0.2);
    canvas.drawPath(path, paint);

    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
