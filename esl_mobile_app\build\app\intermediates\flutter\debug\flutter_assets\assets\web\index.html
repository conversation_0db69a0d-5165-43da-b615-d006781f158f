<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Avatar Lip-Sync</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: transparent;
    }
    #avatar-container {
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/react/18.2.0/umd/react.production.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/react-dom/18.2.0/umd/react-dom.production.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three/0.160.0/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/examples/js/loaders/GLTFLoader.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/examples/js/controls/OrbitControls.min.js"></script>
  <script>
    const phonemeToAnimation = {
      'a': 'A',
      'b': 'B',
      'c': 'C',
      'd': 'D',
      'e': 'E',
      'f': 'F',
      'g': 'G',
      'h': 'H',
      'i': 'I',
      'j': 'J',
      'k': 'K',
      'l': 'L',
      'm': 'M',
      'n': 'N',
      'o': 'O',
      'p': 'P',
      'q': 'Q',
      'r': 'R',
      's': 'S',
      't': 'T',
      'u': 'U',
      'v': 'V',
      'w': 'W',
      'x': 'X',
      'y': 'Y',
      'z': 'Z'
    };

    let scene, camera, renderer, model, mixer, clock;
    const animations = {};

    function initThreeJS() {
      scene = new THREE.Scene();
      camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.setClearColor(0x000000, 0);
      document.getElementById('avatar-container').appendChild(renderer.domElement);

      camera.position.set(0, 1.2, 2);
      camera.lookAt(0, 1.2, 0);

      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambientLight);
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
      directionalLight.position.set(0, 1, 1);
      scene.add(directionalLight);

      const loader = new THREE.GLTFLoader();
      loader.load(
        '/flutter_assets/assets/models/esl_female_model.glb',
        (gltf) => {
          model = gltf.scene;
          scene.add(model);
          model.position.set(0, -0.5, 0);
          model.scale.set(1, 1, 1);

          mixer = new THREE.AnimationMixer(model);
          gltf.animations.forEach((clip) => {
            animations[clip.name] = clip;
          });

          if (animations['Standing_Idle']) {
            const action = mixer.clipAction(animations['Standing_Idle']);
            action.play();
          }
          console.log('Model loaded with animations:', Object.keys(animations));
        },
        undefined,
        (error) => console.error('Error loading GLB:', error)
      );

      clock = new THREE.Clock();

      function animate() {
        requestAnimationFrame(animate);
        const delta = clock.getDelta();
        if (mixer) mixer.update(delta);
        renderer.render(scene, camera);
      }
      animate();

      window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });
    }

    function playAnimation(animationName, duration = null) {
      if (!mixer || !animations[animationName]) {
        console.warn(`Animation ${animationName} not found`);
        return;
      }
      mixer.stopAllAction();
      const action = mixer.clipAction(animations[animationName]);
      action.reset().play();
      if (duration) {
        action.setLoop(THREE.LoopOnce);
        action.clampWhenFinished = true;
        setTimeout(() => {
          playAnimation('Standing_Idle');
        }, duration);
      }
    }

    function playPhonemeSequence(phonemes, totalDuration) {
      if (!mixer) return;
      const numPhonemes = phonemes.length;
      if (numPhonemes === 0) return;
      const timePerPhoneme = totalDuration / numPhonemes;

      phonemes.forEach((phoneme, index) => {
        const animationName = phonemeToAnimation[phoneme] || 'Standing_Idle';
        setTimeout(() => {
          playAnimation(animationName);
        }, index * timePerPhoneme);
      });

      setTimeout(() => {
        playAnimation('Standing_Idle');
      }, totalDuration);
    }

    window.addEventListener('message', (event) => {
      const data = event.data;
      if (data.type === 'phonemes') {
        console.log('Received phonemes:', data.phonemes, 'Duration:', data.duration);
        playPhonemeSequence(data.phonemes, data.duration);
      } else if (data.type === 'animation') {
        console.log('Playing animation:', data.name, 'Duration:', data.duration);
        playAnimation(data.name, data.duration);
      }
    });

    const App = () => {
      React.useEffect(() => {
        initThreeJS();
        window.PhonemeChannel = {
          postMessage: (message) => console.log('JS to Flutter:', message)
        };
      }, []);

      return React.createElement('div', { id: 'avatar-container' });
    };

    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(React.createElement(App));
  </script>
</body>
</html>