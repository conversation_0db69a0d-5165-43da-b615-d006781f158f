import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:pinput/pinput.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart'; // Added for notifications
import '../main.dart';
import '../user_provider.dart';
import 'package:my_esl_app/constants/app_colors.dart'; 

class Onboarding1Page extends StatefulWidget {
  const Onboarding1Page({super.key});

  @override
  State<Onboarding1Page> createState() => _Onboarding1PageState();
}

class _Onboarding1PageState extends State<Onboarding1Page> with CodeAutoFill {
  final TextEditingController _otpController = TextEditingController();
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  bool _isChecked = false;
  bool _isLoading = false;
  bool _showOtpField = false;
  final _formKey = GlobalKey<FormState>();
  String? _completePhoneNumber;
  String? _errorMessage;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin(); // Added for notifications

  static const String _baseUrl = 'http://talktoai.in:4001';

  @override
  void initState() {
    super.initState();
    _requestSmsPermission();
    listenForCode();
    _otpController.addListener(() {
      if (_errorMessage != null) {
        setState(() {
          _errorMessage = null;
        });
      }
    });
    _initializeNotifications(); // Initialize notifications
  }

  Future<void> _initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
    );
    await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<void> _scheduleTrialNotification(DateTime expiryDate) async {
    final notificationTime = expiryDate.subtract(const Duration(days: 1));
    final now = DateTime.now();

    if (notificationTime.isAfter(now)) {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'trial_channel',
        'Trial Plan Notifications',
        channelDescription: 'Notifications for trial plan expiry',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
      );
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await _flutterLocalNotificationsPlugin.schedule(
        0,
        'Trial Plan Expiring Soon',
        'Your trial plan expires tomorrow. Upgrade to a premium plan to continue enjoying all features!',
        notificationTime,
        platformChannelSpecifics,
        androidAllowWhileIdle: true,
      );
    }
  }

  Future<void> _requestSmsPermission() async {
    final status = await Permission.sms.status;
    if (!status.isGranted) {
      await Permission.sms.request();
    }
  }

  @override
  void dispose() {
    SmsAutoFill().unregisterListener();
    _otpController.dispose();
    super.dispose();
  }

  @override
  void codeUpdated() {
    if (code != null && code!.isNotEmpty) {
      setState(() {
        _otpController.text = code!;
      });
      if (_otpController.text.length == 6 && _completePhoneNumber != null) {
        _verifyOtp(_completePhoneNumber!, _otpController.text);
      }
    }
  }

  Future<void> _sendOtp(String phoneNumber) async {
    setState(() => _isLoading = true);
    final String url = '$_baseUrl/send-otp';

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'phone_number': phoneNumber}),
      );

      if (response.statusCode == 200) {
        setState(() => _showOtpField = true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('OTP sent successfully!',
                  style: GoogleFonts.rubik(color: AppColors.teal600))),
        );
        listenForCode();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Failed to send OTP: ${response.body}',
                  style: GoogleFonts.rubik(color: AppColors.teal600))),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Error sending OTP: $e',
                style: GoogleFonts.rubik(color: AppColors.teal600))),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<Map<String, dynamic>?> _fetchPaymentDetails(String token) async {
    final response = await http.get(
      Uri.parse('http://talktoai.in:4002/payment-details'),
      headers: {'Authorization': 'Bearer $token'},
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return null;
    }
  }

  Future<void> _verifyOtp(String phoneNumber, String otp) async {
    if (otp.length != 6) {
      setState(() {
        _errorMessage = 'Please enter a 6-digit OTP.';
      });
      return;
    }
    setState(() {
      _errorMessage = null;
      _isLoading = true;
    });
    final String url = '$_baseUrl/verify-otp';

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'phone_number': phoneNumber, 'otp': otp}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final accessToken = data['access_token'];
        final refreshToken = data['refresh_token'];

        await _storage.write(key: 'access_token', value: accessToken);
        await _storage.write(key: 'refresh_token', value: refreshToken);

        final userProfile = await _fetchUserProfile(accessToken);
        final paymentDetails = await _fetchPaymentDetails(accessToken);
        final userProvider = Provider.of<UserProvider>(context, listen: false);

        await userProvider.setUserDetails(
          userId: userProfile['user_id']?.toString(),
          phoneNumber: userProfile['phone_number'],
          firstName: userProfile['first_name'],
          lastName: userProfile['last_name'],
          email: userProfile['email'],
          nationality: userProfile['nationality'],
          nativeLanguage: userProfile['native_language'],
          levelOfEnglish: userProfile['level_of_english'],
          userName: userProfile['user_name'],
          age: userProfile['age'],
          profilePicture: userProfile['profile_picture'],
          profession: userProfile['profession'],
          schoolClass: userProfile['school_class'],
          reasonForLearning: userProfile['reason_for_learning'],
        );

        if (paymentDetails != null && paymentDetails['expiry_date'] != null) {
          final expiryDate = DateTime.parse(paymentDetails['expiry_date']);
          await _scheduleTrialNotification(expiryDate);
        }

        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check_circle,
                    color: AppColors.teal600, size: 50),
                const SizedBox(height: 16),
                Text(
                  'Verification Successful!',
                  style: GoogleFonts.rubik(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.teal600,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'OK',
                  style: GoogleFonts.rubik(color: AppColors.teal600),
                ),
              ),
            ],
          ),
        );

        if (userProfile['first_name'] != null ||
            userProfile['last_name'] != null) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const MainPage()),
          );
        } else {
          await _showProfileSetupDialog(accessToken);
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const MainPage()),
          );
        }
      } else {
        try {
          final errorData = jsonDecode(response.body);
          final errorMessage = errorData['error'] ?? 'Unknown error';
          setState(() {
            _errorMessage = errorMessage;
          });
        } catch (e) {
          setState(() {
            _errorMessage = 'Failed to verify OTP. Please try again.';
          });
        }
      }
    } catch (e) {
      String errorMsg;
      if (e is http.ClientException) {
        errorMsg = 'Network error. Please check your internet connection.';
      } else {
        errorMsg = 'An unexpected error occurred. Please try again later.';
      }
      setState(() {
        _errorMessage = errorMsg;
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<Map<String, dynamic>> _fetchUserProfile(String token) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/user-profile'),
      headers: {'Authorization': 'Bearer $token'},
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to fetch user profile: ${response.body}');
    }
  }

  Future<void> _showProfileSetupDialog(String token) async {
    final firstNameController = TextEditingController();
    final lastNameController = TextEditingController();
    final emailController = TextEditingController();
    final nationalityController = TextEditingController();
    String? selectedNativeLanguage = 'tamil';
    final levelOfEnglishController = TextEditingController();
    final userNameController = TextEditingController();
    final ageController = TextEditingController();
    final professionController = TextEditingController();
    final reasonForLearningController = TextEditingController();
    String? selectedSchoolClass;
    File? image;
    final picker = ImagePicker();
    bool isUnder18 = false;
    int currentStep = 0;

    int totalSteps = 8;

    final PageController pageController = PageController(initialPage: 0);

    final nativeLanguages = [
      'tamil',
      'hindi',
      'telugu',
      'kannada',
      'malayalam',
      'bengali'
    ];

    final schoolClasses = [
      'Pre-KG',
      'LKG',
      'UKG',
      'Class 1',
      'Class 2',
      'Class 3',
      'Class 4',
      'Class 5',
      'Class 6',
      'Class 7',
      'Class 8',
      'Class 9',
      'Class 10',
      'Class 11',
      'Class 12'
    ];

    void goToNextStep() {
      if (currentStep < totalSteps - 1) {
        pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    void goToPreviousStep() {
      if (currentStep > 0) {
        pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    void submitProfile() {
      final profileDataToSubmit = {
        'first_name': firstNameController.text,
        'last_name': lastNameController.text,
        'email': emailController.text,
        'nationality': nationalityController.text,
        'native_language': selectedNativeLanguage,
        'level_of_english': levelOfEnglishController.text,
        'user_name': userNameController.text,
        'age': int.tryParse(ageController.text),
        'profession': isUnder18 ? null : professionController.text,
        'school_class': isUnder18 ? selectedSchoolClass : null,
        'reason_for_learning': reasonForLearningController.text,
      };

      Navigator.pop(context);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Updating profile...',
                style: GoogleFonts.rubik(color: AppColors.teal600))),
      );

      _processProfileUpdate(
        token: token,
        image: image,
        profileData: profileDataToSubmit,
      );
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          backgroundColor: AppColors.teal50,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            width: double.infinity,
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: BoxDecoration(
              color: AppColors.teal50,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.teal600,
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 10,
                  right: 10,
                  child: IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: AppColors.teal600,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
                Positioned(
                  top: 20,
                  left: 20,
                  right: 20,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Step ${currentStep + 1} of $totalSteps',
                            style: GoogleFonts.rubik(
                              color: AppColors.teal900,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: (currentStep + 1) / totalSteps,
                        backgroundColor: AppColors.teal100,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.teal600,
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 70,
                    left: 20,
                    right: 20,
                    bottom: 80,
                  ),
                  child: PageView(
                    controller: pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    onPageChanged: (index) {
                      setState(() {
                        currentStep = index;
                      });
                    },
                    children: [
                      _buildProfileStep(
                        title: "Add Your Profile Picture",
                        subtitle: "Let's start with a photo of you",
                        content: Center(
                          child: GestureDetector(
                            onTap: () async {
                              final pickedFile = await picker.pickImage(
                                source: ImageSource.gallery,
                              );
                              if (pickedFile != null) {
                                setState(() {
                                  image = File(pickedFile.path);
                                });
                              }
                            },
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppColors.teal100,
                                    border: Border.all(
                                      color: AppColors.teal600,
                                      width: 2,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            AppColors.primary.withOpacity(0.3),
                                        blurRadius: 10,
                                        spreadRadius: 2,
                                      ),
                                    ],
                                  ),
                                  child: image != null
                                      ? ClipOval(
                                          child: Image.file(
                                            image!,
                                            fit: BoxFit.cover,
                                            width: 150,
                                            height: 150,
                                          ),
                                        )
                                      : const Icon(
                                          Icons.add_a_photo,
                                          size: 50,
                                          color: AppColors.teal600,
                                        ),
                                ),
                                if (image != null)
                                  Positioned(
                                    bottom: 0,
                                    right: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: AppColors.teal50,
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: AppColors.teal600,
                                          width: 1,
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.edit,
                                        color: AppColors.teal600,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      _buildProfileStep(
                        title: "What's Your Name?",
                        subtitle: "Please enter your first and last name",
                        content: Column(
                          children: [
                            _buildStyledTextField(
                              controller: firstNameController,
                              labelText: 'First Name',
                              icon: Icons.person,
                            ),
                            const SizedBox(height: 20),
                            _buildStyledTextField(
                              controller: lastNameController,
                              labelText: 'Last Name',
                              icon: Icons.person_outline,
                            ),
                          ],
                        ),
                      ),
                      _buildProfileStep(
                        title: "Create Your Username",
                        subtitle: "Choose a username and provide your email",
                        content: Column(
                          children: [
                            _buildStyledTextField(
                              controller: userNameController,
                              labelText: 'Username',
                              icon: Icons.alternate_email,
                            ),
                            const SizedBox(height: 20),
                            _buildStyledTextField(
                              controller: emailController,
                              labelText: 'Email',
                              icon: Icons.email,
                              keyboardType: TextInputType.emailAddress,
                            ),
                          ],
                        ),
                      ),
                      _buildProfileStep(
                        title: "How Old Are You?",
                        subtitle: "Please enter your age",
                        content: _buildStyledTextField(
                          controller: ageController,
                          labelText: 'Age',
                          icon: Icons.cake,
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final age = int.tryParse(value);
                            if (age != null) {
                              setState(() {
                                isUnder18 = age < 18;
                                totalSteps = isUnder18 ? 8 : 8;
                              });
                            }
                          },
                        ),
                      ),
                      _buildProfileStep(
                        title: isUnder18
                            ? "What Class Are You In?"
                            : "What's Your Profession?",
                        subtitle: isUnder18
                            ? "Please select your school class"
                            : "Please enter your profession",
                        content: isUnder18
                            ? _buildStyledDropdown(
                                value: selectedSchoolClass,
                                items: schoolClasses,
                                labelText: 'School Class',
                                onChanged: (String? newValue) {
                                  setState(() {
                                    selectedSchoolClass = newValue;
                                  });
                                },
                              )
                            : _buildStyledTextField(
                                controller: professionController,
                                labelText: 'Profession',
                                icon: Icons.work,
                              ),
                      ),
                      _buildProfileStep(
                        title: "What's Your Nationality?",
                        subtitle: "Please enter your nationality",
                        content: _buildStyledTextField(
                          controller: nationalityController,
                          labelText: 'Nationality',
                          icon: Icons.flag,
                        ),
                      ),
                      _buildProfileStep(
                        title: "What's Your Native Language?",
                        subtitle: "Please select your native language",
                        content: _buildStyledDropdown(
                          value: selectedNativeLanguage,
                          items: nativeLanguages,
                          labelText: 'Native Language',
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedNativeLanguage = newValue;
                            });
                          },
                        ),
                      ),
                      _buildProfileStep(
                        title: "About Your English Learning",
                        subtitle: "Tell us about your current level and goals",
                        content: Column(
                          children: [
                            _buildStyledTextField(
                              controller: levelOfEnglishController,
                              labelText: 'Level of English',
                              icon: Icons.school,
                              hintText: 'Beginner, Intermediate, Advanced...',
                              maxLines: 3,
                            ),
                            const SizedBox(height: 20),
                            _buildStyledTextField(
                              controller: reasonForLearningController,
                              labelText: 'Why do you want to learn English?',
                              icon: Icons.lightbulb_outline,
                              hintText: 'For work, travel, education...',
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (currentStep > 0)
                        ElevatedButton(
                          onPressed: goToPreviousStep,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.teal600,
                            foregroundColor: AppColors.white,
                            minimumSize: const Size(0, 50),
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                              side: BorderSide(
                                  color: AppColors.teal600, width: 1.5),
                            ),
                          ).copyWith(
                            overlayColor:
                                WidgetStateProperty.resolveWith<Color?>(
                              (Set<WidgetState> states) {
                                if (states.contains(WidgetState.pressed)) {
                                  return Colors.tealAccent.withOpacity(0.3);
                                }
                                return null;
                              },
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.arrow_back,
                                  size: 16, color: AppColors.white),
                              const SizedBox(width: 8),
                              Text(
                                'Back',
                                style:
                                    GoogleFonts.rubik(color: AppColors.white),
                              ),
                            ],
                          ),
                        )
                      else
                        const SizedBox(width: 100),
                      ElevatedButton(
                        onPressed: currentStep == totalSteps - 1
                            ? submitProfile
                            : goToNextStep,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.teal600,
                          foregroundColor: AppColors.white,
                          minimumSize: const Size(0, 50),
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                            side: BorderSide(
                                color: AppColors.teal600, width: 1.5),
                          ),
                        ).copyWith(
                          overlayColor: WidgetStateProperty.resolveWith<Color?>(
                            (Set<WidgetState> states) {
                              if (states.contains(WidgetState.pressed)) {
                                return Colors.tealAccent.withOpacity(0.3);
                              }
                              return null;
                            },
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              currentStep == totalSteps - 1 ? 'Submit' : 'Next',
                              style: GoogleFonts.rubik(color: AppColors.white),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              currentStep == totalSteps - 1
                                  ? Icons.check
                                  : Icons.arrow_forward,
                              size: 16,
                              color: AppColors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileStep({
    required String title,
    required String subtitle,
    required Widget content,
  }) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.rubik(
              fontSize: 24,
              color: AppColors.teal900,
              shadows: [
                Shadow(
                  blurRadius: 10.0,
                  color: AppColors.primary.withOpacity(0.3),
                  offset: const Offset(0, 0),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.rubik(
              fontSize: 16,
              color: AppColors.teal700,
            ),
          ),
          const SizedBox(height: 30),
          content,
        ],
      ),
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String labelText,
    IconData? icon,
    String? hintText,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    Function(String)? onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.teal50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: AppColors.teal600,
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        onChanged: onChanged,
        style: GoogleFonts.rubik(color: AppColors.teal900),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          hintStyle: GoogleFonts.rubik(
            color: AppColors.teal700.withOpacity(0.5),
          ),
          labelStyle: GoogleFonts.rubik(color: AppColors.teal700),
          prefixIcon:
              icon != null ? Icon(icon, color: AppColors.teal600) : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildStyledDropdown({
    required String? value,
    required List<String> items,
    required String labelText,
    required Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.teal50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: AppColors.teal600,
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: DropdownButtonFormField<String>(
        value: value,
        dropdownColor: AppColors.teal50,
        style: GoogleFonts.rubik(color: AppColors.teal900),
        decoration: InputDecoration(
          labelText: labelText,
          labelStyle: GoogleFonts.rubik(color: AppColors.teal700),
          border: InputBorder.none,
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.rubik(color: AppColors.teal900),
            ),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Future<void> _processProfileUpdate({
    required String token,
    required File? image,
    required Map<String, dynamic> profileData,
  }) async {
    try {
      if (image != null) {
        final bytes = await image.readAsBytes();
        final base64Image = base64Encode(bytes);
        profileData['profile_picture'] = base64Image;
      }

      await _updateUserProfile(token, profileData);

      final updatedProfile = await _fetchUserProfile(token);

      if (!mounted) return;

      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.setUserDetails(
        userId: updatedProfile['user_id']?.toString(),
        phoneNumber: updatedProfile['phone_number'],
        firstName: updatedProfile['first_name'],
        lastName: updatedProfile['last_name'],
        email: updatedProfile['email'],
        nationality: updatedProfile['nationality'],
        nativeLanguage: updatedProfile['native_language'],
        levelOfEnglish: updatedProfile['level_of_english'],
        userName: updatedProfile['user_name'],
        age: updatedProfile['age'],
        profilePicture: updatedProfile['profile_picture'],
        profession: updatedProfile['profession'],
        schoolClass: updatedProfile['school_class'],
        reasonForLearning: updatedProfile['reason_for_learning'],
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Profile updated successfully!',
                  style: GoogleFonts.rubik(color: AppColors.teal600))),
        );

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const MainPage()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error updating profile: $e',
                  style: GoogleFonts.rubik(color: AppColors.teal600))),
        );
      }
    }
  }

  Future<void> _updateUserProfile(
      String token, Map<String, dynamic> profileData) async {
    final response = await http.put(
      Uri.parse('$_baseUrl/update-profile'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(profileData),
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to update profile: ${response.body}');
    }
  }

  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.teal50,
                AppColors.white,
              ],
            ),
          ),
          child: Stack(
            children: [
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            customBackButton(),
                            customHomeButton(),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Text(
                          'Hello there',
                          style: GoogleFonts.rubik(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: AppColors.teal900,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          'PLEASE ENTER PHONE NUMBER.',
                          style: GoogleFonts.rubik(
                            fontSize: 16,
                            color: AppColors.teal700,
                          ),
                        ),
                        const SizedBox(height: 20),
                        IntlPhoneField(
                          decoration: customTextFieldDecoration(),
                          initialCountryCode: 'US',
                          onChanged: (phone) {
                            setState(() {
                              _completePhoneNumber = phone.completeNumber;
                            });
                          },
                          validator: (phone) {
                            if (phone == null || phone.number.isEmpty) {
                              return 'Please enter a phone number';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        Center(child: customGetOtpButton()),
                        const SizedBox(height: 20),
                        if (_showOtpField)
                          Column(
                            children: [
                              Center(
                                child: Pinput(
                                  length: 6,
                                  controller: _otpController,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  defaultPinTheme: PinTheme(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      border: Border.all(
                                        color: AppColors.teal600,
                                        width: 1.5,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    textStyle: GoogleFonts.rubik(
                                      fontSize: 20,
                                      color: AppColors.teal900,
                                    ),
                                  ),
                                  focusedPinTheme: PinTheme(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      border: Border.all(
                                        color: AppColors.teal800,
                                        width: 2,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    textStyle: GoogleFonts.rubik(
                                      fontSize: 20,
                                      color: AppColors.teal900,
                                    ),
                                  ),
                                  onCompleted: (value) {
                                    if (_completePhoneNumber != null) {
                                      _verifyOtp(_completePhoneNumber!, value);
                                    }
                                  },
                                ),
                              ),
                              if (_errorMessage != null)
                                Container(
                                  margin: const EdgeInsets.only(top: 10),
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.red),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.error_outline,
                                          color: AppColors.red),
                                      const SizedBox(width: 5),
                                      Expanded(
                                        child: Text(
                                          _errorMessage!,
                                          style: GoogleFonts.rubik(
                                            color: AppColors.red,
                                            fontSize: 16,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        const SizedBox(height: 20),
                        Row(
                          children: [customCheckbox(), customAgreementText()],
                        ),
                        const SizedBox(height: 30),
                        Center(child: customVerifyButton()),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget customBackButton() => IconButton(
        icon: Icon(Icons.arrow_back, color: AppColors.teal600),
        onPressed: () => Navigator.pop(context),
      );

  Widget customHomeButton() => IconButton(
        icon: Icon(Icons.home, color: AppColors.teal600),
        onPressed: () => Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const MainPage()),
        ),
      );

  InputDecoration customTextFieldDecoration() => InputDecoration(
        filled: true,
        fillColor: AppColors.teal50,
        hintText: 'Enter phone number',
        hintStyle: GoogleFonts.rubik(
          color: AppColors.teal700.withOpacity(0.5),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30),
          borderSide: BorderSide(color: AppColors.teal600, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30),
          borderSide: BorderSide(color: AppColors.teal800, width: 2),
        ),
      );

  Widget customGetOtpButton() => SizedBox(
        width: MediaQuery.of(context).size.width * 0.5,
        child: ElevatedButton(
          onPressed: _isLoading
              ? null
              : () {
                  if (_formKey.currentState!.validate() &&
                      _completePhoneNumber != null) {
                    _sendOtp(_completePhoneNumber!);
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.teal600,
            foregroundColor: AppColors.white,
            minimumSize: const Size(0, 50),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
              side: BorderSide(color: AppColors.teal600, width: 1.5),
            ),
          ).copyWith(
            overlayColor: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.pressed)) {
                  return AppColors.teal600.withOpacity(0.3);
                }
                return null;
              },
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'GET OTP',
                style: GoogleFonts.rubik(
                  fontSize: 16,
                  color: AppColors.white,
                ),
              ),
              const Icon(Icons.lock, color: AppColors.white),
            ],
          ),
        ),
      );

  Widget customCheckbox() => Checkbox(
        value: _isChecked,
        onChanged: (bool? value) => setState(() => _isChecked = value ?? false),
        activeColor: AppColors.tealAccent,
        checkColor: AppColors.white,
        side: BorderSide(color: AppColors.tealAccent),
      );

  Widget customAgreementText() => Expanded(
        child: Text(
          'I agree to ESL Learning Public Agreement, Terms, & Privacy Policy',
          style: GoogleFonts.rubik(
            fontSize: 14,
            color: AppColors.tealAccent.shade700,
          ),
        ),
      );

  Widget customVerifyButton() => ElevatedButton(
        onPressed: _isChecked &&
                !_isLoading &&
                _showOtpField &&
                _completePhoneNumber != null &&
                _otpController.text.length == 6
            ? () => _verifyOtp(_completePhoneNumber!, _otpController.text)
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.teal600,
          foregroundColor: AppColors.white,
          minimumSize: const Size(0, 50),
          padding: const EdgeInsets.symmetric(horizontal: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
            side: BorderSide(color: AppColors.teal600, width: 1.5),
          ),
        ).copyWith(
          overlayColor: WidgetStateProperty.resolveWith<Color?>(
            (Set<WidgetState> states) {
              if (states.contains(WidgetState.pressed)) {
                return AppColors.tealAccent.withOpacity(0.3);
              }
              return null;
            },
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Verify',
                style: GoogleFonts.rubik(
                  fontSize: 18,
                  color: AppColors.white,
                ),
              ),
      );
}