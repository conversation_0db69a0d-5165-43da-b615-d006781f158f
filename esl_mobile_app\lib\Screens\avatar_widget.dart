import 'package:flutter/material.dart';
import 'package:o3d/o3d.dart';
import 'dart:async';

class AvatarWidget extends StatefulWidget {
  const AvatarWidget({super.key});

  @override
  State<AvatarWidget> createState() => _AvatarWidgetState();
}

class _AvatarWidgetState extends State<AvatarWidget> {
  final O3DController controller = O3DController();
  final String _modelPath =
      'assets/models/davidfinalglb.glb'; // Default male model
  final String _idleAnimation = 'Standing_Idle'; // Default idle animation

  final bool _debugMode = false; // Set to true for O3D logs

  void _debugLog(String message) {
    if (_debugMode) {
      debugPrint('ESL Avatar Widget: $message');
    }
  }

  @override
  void initState() {
    super.initState();
    controller.logger = (message) {
      _debugLog('O3D: $message');
    };

    // Set initial camera and animation after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Delay slightly to ensure the O3D view is ready
      Future.delayed(const Duration(milliseconds: 500), () {
        if (!mounted) return;
        try {
          // Slightly adjusted camera for embedding: looking slightly up, zoomed out a bit
          controller.cameraOrbit(0, 80, 120); // theta, phi, radius
          controller.cameraTarget(0, 1.1, 0); // x, y, z (target point)
          _debugLog('Initial camera set.');

          // Play the idle animation
          controller.animationName = _idleAnimation;
          controller.play();
          _debugLog('Playing animation: $_idleAnimation');
        } catch (e) {
          _debugLog('Error setting initial camera/animation: $e');
        }
      });
    });
  }

  @override
  void dispose() {
    // Consider pausing or resetting controller if necessary,
    // but typically dispose handles resources.
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Using a ValueKey ensures the widget rebuilds if the model path changes,
    // although in this simple version, it doesn't change.
    return O3D.asset(
      key: ValueKey(_modelPath),
      src: _modelPath,
      controller: controller,
      // Disable user interaction with camera
      cameraControls: false,
      // Crucial for layering: make the background transparent
      backgroundColor: Colors.transparent,
      // Disable AR features
      ar: false,
      // Optional: Add autoPlay if needed, but we handle play in initState
      // autoPlay: true,
      // Optional: Set animation name directly if autoPlay is true
      animationName: _idleAnimation,
      // Note: o3d 3.1.3 doesn't support loading/error builders
      // We'll handle errors in the onWebViewCreated callback
      onWebViewCreated: (webViewController) {
        _debugLog('O3D WebView created.');
        // You could re-apply settings here if needed, but initState usually suffices
      },
    );
  }
}
