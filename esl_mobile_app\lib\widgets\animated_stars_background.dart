import 'package:flutter/material.dart';
//import '../utils/stars_painter.dart';

class AnimatedStarsBackground extends StatefulWidget {
  const AnimatedStarsBackground({super.key});

  @override
  State<AnimatedStarsBackground> createState() =>
      _AnimatedStarsBackgroundState();
}

class _AnimatedStarsBackgroundState extends State<AnimatedStarsBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(
          seconds: 500), // Further increased duration for even slower animation
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          //painter: AnimatedStarsPainter(animation: _controller.value),
          size: Size(MediaQuery.of(context).size.width,
              MediaQuery.of(context).size.height),
        );
      },
    );
  }
}

class StaticStarsBackground extends StatelessWidget {
  const StaticStarsBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      //painter: StarsPainter(),
      size: Size(MediaQuery.of(context).size.width,
          MediaQuery.of(context).size.height),
    );
  }
}

class NeonGlowElement extends StatelessWidget {
  final double top;
  final double left;
  final double width;
  final double height;
  final Color color;
  final double opacity;
  final double blurRadius;
  final double spreadRadius;

  const NeonGlowElement({
    super.key,
    required this.top,
    required this.left,
    this.width = 150,
    this.height = 150,
    this.color = const Color(0xFF9C27B0),
    this.opacity = 0.3,
    this.blurRadius = 90,
    this.spreadRadius = 45,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: top,
      left: left,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(opacity),
              blurRadius: blurRadius,
              spreadRadius: spreadRadius,
            ),
          ],
        ),
      ),
    );
  }
}
