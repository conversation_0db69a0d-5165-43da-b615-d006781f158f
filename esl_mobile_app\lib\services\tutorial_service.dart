import 'package:shared_preferences/shared_preferences.dart';

/// Service to manage the tutorial state
class TutorialService {
  static const String _tutorialShownKey = 'tutorial_shown';

  /// Check if the tutorial has been shown before
  static Future<bool> isTutorialShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_tutorialShownKey) ?? false;
  }

  /// Mark the tutorial as shown
  static Future<void> markTutorialAsShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_tutorialShownKey, true);
  }

  /// Reset the tutorial state (for testing purposes)
  static Future<void> resetTutorial() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_tutorialShownKey, false);
  }
}
