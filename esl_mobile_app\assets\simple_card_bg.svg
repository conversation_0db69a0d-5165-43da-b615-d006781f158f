<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F3E5F5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F2F1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="200" rx="16" ry="16" fill="url(#cardGradient)" />
  
  <!-- Decorative elements -->
  <circle cx="50" cy="50" r="20" fill="#81C784" opacity="0.3" />
  <circle cx="250" cy="150" r="15" fill="#64B5F6" opacity="0.3" />
  <circle cx="200" cy="40" r="12" fill="#BA68C8" opacity="0.3" />
  
  <!-- Book icon -->
  <g transform="translate(20, 20)">
    <rect x="0" y="0" width="30" height="20" rx="2" fill="#4CAF50" opacity="0.6" />
    <rect x="2" y="2" width="26" height="16" fill="white" opacity="0.8" />
    <line x1="5" y1="6" x2="25" y2="6" stroke="#4CAF50" stroke-width="1" />
    <line x1="5" y1="10" x2="20" y2="10" stroke="#4CAF50" stroke-width="1" />
    <line x1="5" y1="14" x2="22" y2="14" stroke="#4CAF50" stroke-width="1" />
  </g>
</svg>
