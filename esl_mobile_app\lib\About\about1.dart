import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:my_esl_app/constants/app_colors.dart';

class About1Page extends StatelessWidget {
  const About1Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background Layer
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 134, 217, 209), // Lighter teal
                  Color.fromARGB(255, 24, 195, 178), // Medium teal
                  Color.fromARGB(255, 3, 127, 106), // Darker teal
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          // Foreground Layer
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Stack for the three images
                    SizedBox(
                      height: 300,
                      width: 400,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Left image
                          Positioned(
                            left: 0,
                            child: Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                image: const DecorationImage(
                                  image:
                                      AssetImage('assets/images/about_3.png'),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          // Right image
                          Positioned(
                            right: 0,
                            child: Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                image: const DecorationImage(
                                  image: AssetImage('assets/about_2.png'),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                          // Center image
                          Positioned(
                            child: Container(
                              width: 300,
                              height: 300,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                image: const DecorationImage(
                                  image: AssetImage('assets/about_1.png'),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Text with Rubik font
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Text(
                        'Speak Confidently, Learn Effortlessly,\nPowered by AI!',
                        textAlign: TextAlign.center,
                        style: GoogleFonts.rubik(
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
