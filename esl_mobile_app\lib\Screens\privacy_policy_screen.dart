import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'report_screen.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  _PrivacyPolicyScreenState createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  final List<bool> _expandedSections = [false, false, false, false];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 50, 107, 87),
                  Color.fromARGB(255, 34, 136, 117),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
          ),
          title: const Text(
            'Privacy Policy',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFFc4ecde),
            ),
          ),
          centerTitle: true,
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ReportScreen(),
            ),
          );
        },
        backgroundColor: const Color.fromARGB(255, 34, 136, 117),
        child: const Icon(Icons.report, color: Color(0xFFc4ecde)),
        tooltip: 'Report an Issue',
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          double padding = constraints.maxWidth > 600 ? 32.0 : 16.0;
          double titleFontSize = constraints.maxWidth > 600 ? 24 : 18;
          double contentFontSize = constraints.maxWidth > 600 ? 18 : 16;

          return Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFFc4ecde),
                  Colors.white,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: ListView(
                children: [
                  _buildSection(
                    index: 0,
                    title: "Privacy Policy",
                    icon: Icons.lock,
                    content: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBoldText("Information We Collect"),
                        _buildText(
                            "- Personal Data: Name, email, phone number"),
                        _buildText(
                            "- Usage Data: Browsing behavior, app activity"),
                        _buildText(
                            "- Device Data: IP address, browser type, device details"),
                        const SizedBox(height: 10),
                        _buildBoldText("How We Use Your Information"),
                        _buildText("- Provide and maintain services"),
                        _buildText("- Process payments via Razorpay"),
                        _buildText("- Notify users about updates"),
                        const SizedBox(height: 10),
                        _buildBoldText("Data Security"),
                        _buildText(
                            "- We follow industry-standard security measures."),
                        const SizedBox(height: 10),
                        _buildBoldText("Your Rights"),
                        _buildText(
                            "- You can update or request deletion of your personal data at:"),
                        _buildClickableEmail(
                            "<EMAIL>"),
                      ],
                    ),
                  ),
                  _buildSection(
                    index: 1,
                    title: "Refund & Cancellation Policy",
                    icon: Icons.money_off,
                    content: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBoldText("Refund Policy"),
                        _buildText(
                            "- Refunds are calculated based on unused tokens."),
                        _buildText("- Refunds processed in 5-7 business days."),
                        const SizedBox(height: 10),
                        _buildBoldText("Cancellation Policy"),
                        _buildText(
                            "- Service continues until the end of the billing cycle."),
                        _buildText(
                            "- Unused tokens are not refunded but remain usable."),
                        const SizedBox(height: 10),
                        _buildBoldText("How to Request a Refund?"),
                        _buildClickableEmail(
                            "<EMAIL>"),
                      ],
                    ),
                  ),
                  _buildSection(
                    index: 2,
                    title: "Contact Us",
                    icon: Icons.phone,
                    content: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBoldText("Email:"),
                        _buildClickableEmail(
                            "<EMAIL>"),
                        const SizedBox(height: 10),
                        _buildBoldText("Business Hours:"),
                        _buildText(
                            "🕘 Monday - Friday, 9:00 AM - 6:00 PM (GMT+5:30)"),
                      ],
                    ),
                  ),
                  _buildSection(
                    index: 3,
                    title: "Plan Details",
                    icon: Icons.star,
                    content: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBoldText("Small Plan - ₹50"),
                        _buildText(
                            "✅ 1 General Interview 35 credits 1 Group Discussion 70 credits With 50 credits, you can access 1 General Interview Credits can be used for General Interviews or Group Discussions."),
                        const SizedBox(height: 10),
                        _buildBoldText("Medium Plan - ₹100"),
                        _buildText(
                            "✅ Provides 105 credits,1 General Interview 35 credits 1 Group Discussion 70 credits With 105 credits, you can access 2 General Interviews (70 credits) or 1 Group Discussion (70 credits) Credits can be used for General Interviews or Group Discussions."),
                        const SizedBox(height: 10),
                        _buildBoldText("Large Plan - ₹250"),
                        _buildText(
                            "✅ Provides 280 credits.1 General Interview 35 credits.1 Group Discussion 70 credits.With 280 credits, you can access 8 General Interviews (280 credits) or 4 Group Discussions (280 credits).Credits can be used for General Interviews or Group Discussions."),
                        const SizedBox(height: 10),
                        _buildBoldText("Enterprise Plan - ₹350"),
                        _buildText(
                            "✅ Provides 385 credits.1 General Interview 35 credits.1 Group Discussion 70 credits.With 385 credits, you can access 11 General Interviews (385 credits) or 5 Group Discussions (350 credits).Credits can be used for General Interviews or Group Discussions."),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSection({
    required int index,
    required String title,
    required Widget content,
    required IconData icon,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      color: const Color(0xFFc4ecde).withOpacity(0.9),
      child: ExpansionTile(
        leading: Icon(icon, color: const Color.fromARGB(255, 50, 107, 87)),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color.fromARGB(255, 50, 107, 87),
          ),
        ),
        initiallyExpanded: _expandedSections[index],
        onExpansionChanged: (bool expanded) {
          setState(() {
            _expandedSections[index] = expanded;
          });
        },
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildText(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          color: Color.fromARGB(255, 34, 136, 117),
        ),
      ),
    );
  }

  Widget _buildBoldText(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Color.fromARGB(255, 50, 107, 87),
        ),
      ),
    );
  }

  Widget _buildClickableEmail(String email) {
    return InkWell(
      onTap: () => launchUrl(Uri.parse("mailto:$email")),
      child: Text(
        email,
        style: const TextStyle(
          fontSize: 16,
          color: Color.fromARGB(255, 34, 136, 117),
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }
}
