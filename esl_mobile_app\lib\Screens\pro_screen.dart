import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:http/http.dart' as http;
import 'package:my_esl_app/Screens/plans_data.dart'; // Assuming this file exists and is correct
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart'; // Added for Lottie animation
import '../user_provider.dart'; // Assuming this file exists and is correct
import 'privacy_policy_screen.dart'; // Added import for PrivacyPolicyScreen
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:ui'; // For ImageFilter

// --- Color Constants ---
const Color textColor = Color.fromARGB(255, 228, 230, 232); // Dark Blue
const Color buttonColor = Color(0xFF388E3C); // Green
const Color priceColor = Color(0xFFD32F2F); // Red Accent
const Color oldPriceColor = Colors.grey;
const Color successColor = Color(0xFF2E7D32); // Darker Green for Success
const Color errorColor = Color(0xFFC62828); // Darker Red for Error
const Color warningColor = Colors.orange; // For the offer banner
const Color platecolor = Color(0xFF265073);

// --- Custom PlanCard Widget (with Glass Effect) ---
class PlanCard extends StatefulWidget {
  final Map<String, dynamic> plan;
  final Function(Map<String, dynamic>) onTap;
  final String currency;

  const PlanCard({
    super.key,
    required this.plan,
    required this.onTap,
    required this.currency,
  });

  @override
  _PlanCardState createState() => _PlanCardState();
}

class _PlanCardState extends State<PlanCard> {
  bool isTapped = false;

  @override
  Widget build(BuildContext context) {
    // Determine price and old price based on currency
    final String displayPrice = widget.currency == "INR"
        ? "₹ ${widget.plan["price"]}"
        : "\$ ${widget.plan["price-USD"]}";
    final String displayOldPrice = widget.currency == "INR"
        ? "₹ ${widget.plan["oldPrice"]}"
        : "\$ ${widget.plan["oldPrice-USD"]}";

    return GestureDetector(
      onTapDown: (_) => setState(() => isTapped = true),
      onTapUp: (_) {
        setState(() => isTapped = false);
        widget.onTap(widget.plan); // Trigger the payment process
      },
      onTapCancel: () => setState(() => isTapped = false),
      child: Transform.scale(
        scale: isTapped ? 0.95 : 1.0, // Scale effect on tap
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20), // Slightly more rounded
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8), // Adjusted blur
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.25), // Glass effect
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withOpacity(0.4), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    offset: const Offset(0, 6),
                    blurRadius: 12,
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.plan["name"],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 240, 240, 240),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    displayPrice,
                    style: const TextStyle(
                      fontSize: 26,
                      fontWeight: FontWeight.bold,
                      color: priceColor,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    displayOldPrice,
                    style: const TextStyle(
                      decoration: TextDecoration.lineThrough,
                      color: oldPriceColor,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 15),
                  Expanded(
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: (widget.plan["detail"] as List).length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(Icons.check_circle, color: Colors.green[800], size: 18),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.plan["detail"][index],
                                  style: const TextStyle(fontSize: 14, color: textColor),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 15),
                  ElevatedButton(
                    onPressed: () => widget.onTap(widget.plan),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: buttonColor,
                      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      elevation: 5,
                    ),
                    child: const Text(
                      "Buy Now",
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// --- Main ProScreen Widget ---
class ProScreen extends StatefulWidget {
  const ProScreen({super.key});

  @override
  _PaymentPageState createState() => _PaymentPageState();
}

class _PaymentPageState extends State<ProScreen> with TickerProviderStateMixin {
  bool popupVisible = false;
  String currency = "INR";
  Map<String, dynamic>? selectedPlan;
  List<dynamic> plans = [];
  late Razorpay _razorpay;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _backgroundController;
  Alignment _gradientBegin = Alignment.topLeft;
  Alignment _gradientEnd = Alignment.bottomRight;
  bool _isLoading = true;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  bool _showTrialNotification = false; // For trial plan notification
  DateTime? _trialExpiryDate;

  static const String _baseUrl = 'http://talktoai.in:4001';

  @override
  void initState() {
    super.initState();
    _initializeScreen();

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    )..repeat(reverse: true);

    _backgroundController.addListener(() {
      setState(() {
        _gradientBegin = Alignment(
          _backgroundController.value * 2 - 1,
          _backgroundController.value * -2 + 1,
        );
        _gradientEnd = Alignment(
          _backgroundController.value * -2 + 1,
          _backgroundController.value * 2 - 1,
        );
      });
    });

    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  Future<void> _initializeScreen() async {
    setState(() => _isLoading = true);
    plans = PlanData.plans;
    await fetchLocation();
    await _checkTrialStatus(); // Check trial status
    setState(() => _isLoading = false);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _razorpay.clear();
    _fadeController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  // --- Check Trial Plan Status ---
  Future<void> _checkTrialStatus() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    final accessToken = await _storage.read(key: 'access_token');

    if (userId == null || accessToken == null) return;

    try {
      final response = await http.get(
        Uri.parse('http://talktoai.in:4002/check_trial_status'),
        headers: {'Authorization': 'Bearer $accessToken'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['is_trial_active']) {
          final expiryDate = DateTime.parse(data['expiry_date']);
          final now = DateTime.now();
          final daysLeft = expiryDate.difference(now).inDays;

          setState(() {
            _trialExpiryDate = expiryDate;
            _showTrialNotification = daysLeft <= 2 && daysLeft > 0;
          });

          if (_showTrialNotification) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Your trial plan expires on ${expiryDate.toString().substring(0, 10)}. Upgrade now to continue enjoying premium features!',
                    style: const TextStyle(color: Colors.white),
                  ),
                  backgroundColor: warningColor,
                  duration: const Duration(seconds: 5),
                  action: SnackBarAction(
                    label: 'Upgrade',
                    textColor: Colors.white,
                    onPressed: () {
                      // Already on ProScreen, so no navigation needed
                    },
                  ),
                ),
              );
            });
          }
        }
      }
    } catch (e) {
      print('Error checking trial status: $e');
    }
  }

  Future<void> fetchLocation() async {
    try {
      final response = await http.get(Uri.parse("https://ipapi.co/json/")).timeout(const Duration(seconds: 5));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final country = data["country_code"];
        setState(() {
          currency = (country == "IN") ? "INR" : "USD";
        });
      } else {
        setState(() {
          currency = "INR";
        });
      }
    } catch (e) {
      print("Error fetching location: $e");
      setState(() {
        currency = "INR";
      });
    }
  }

  void handleCardClick(Map<String, dynamic> plan) {
    setState(() {
      selectedPlan = plan;
    });
    handlePayment(plan);
  }

  Future<void> handlePayment(Map<String, dynamic> plan) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    final userName = userProvider.firstName ?? 'User';
    final userEmail = userProvider.email ?? '<EMAIL>';
    final userContact = userProvider.phoneNumber ?? '**********';

    if (userId == null) {
      _showErrorDialog("User not logged in. Please log in again.");
      return;
    }

    bool isInUSD = currency == "USD";
    String priceStr = (isInUSD ? plan["price-USD"] : plan["price"]).toString();
    priceStr = priceStr.replaceAll(RegExp(r'[^0-9.]'), '');
    double price = double.tryParse(priceStr) ?? 0.0;

    if (price <= 0) {
      _showErrorDialog("Invalid plan price. Please contact support.");
      return;
    }

    int amountInSmallestUnit = (price * 100).round();

    setState(() => _isLoading = true);

    try {
      final response = await http.post(
        Uri.parse('http://talktoai.in:4002/create_order'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'amount': amountInSmallestUnit,
          'currency': currency,
          'user_id': userId,
          'plan_name': plan['name'],
        }),
      ).timeout(const Duration(seconds: 15));

      setState(() => _isLoading = false);

      if (response.statusCode == 200) {
        final orderData = json.decode(response.body);
        final orderId = orderData["order_id"];

        if (orderId == null || orderId.isEmpty) {
          throw Exception("Order ID not received from server.");
        }

        var options = {
          'key': 'rzp_test_m7ArlesvsvWAfQ',
          'amount': amountInSmallestUnit.toString(),
          'currency': currency,
          'name': 'ESL Learning App',
          'description': 'Payment for ${plan["name"]}',
          'order_id': orderId,
          'prefill': {
            'name': userName,
            'email': userEmail,
            'contact': userContact,
          },
          'theme': {'color': '#3399cc'}
        };
        _razorpay.open(options);
      } else {
        final errorBody = json.decode(response.body);
        throw Exception("Failed to create order: ${errorBody['error'] ?? response.reasonPhrase}");
      }
    } catch (error) {
      setState(() => _isLoading = false);
      print("Order Creation Error: $error");
      _showErrorDialog("Could not initiate payment. Please check your connection and try again. [$error]");
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;

    if (userId == null || selectedPlan == null) {
      _showErrorDialog("An internal error occurred after payment. Please contact support.");
      return;
    }

    setState(() => _isLoading = true);

    Map<String, dynamic> paymentDetails = {
      'payment_id': response.paymentId,
      'order_id': response.orderId,
      'signature': response.signature,
      'plan_name': selectedPlan!["name"],
      'plan_id': selectedPlan!["razorpayProductId"],
      'user_id': userId,
      'currency': currency,
      'amount': (double.tryParse((currency == "USD"
                  ? selectedPlan!["price-USD"]
                  : selectedPlan!["price"])
              .toString()
              .replaceAll(RegExp(r'[^0-9.]'), '')) ??
          0.0) *
          100.round()
    };

    try {
      final backendResponse = await http.post(
        Uri.parse('http://talktoai.in:4002/payment_success'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(paymentDetails),
      ).timeout(const Duration(seconds: 15));

      setState(() => _isLoading = false);

      if (backendResponse.statusCode == 200) {
        _showSuccessDialog("Payment Successful!", "Thank you for subscribing to ${selectedPlan!['name']}. Your access has been activated.");
        setState(() {
          _showTrialNotification = false; // Hide trial notification after successful payment
        });
      } else {
        final errorBody = json.decode(backendResponse.body);
        throw Exception("Failed to verify payment on server: ${errorBody['error'] ?? backendResponse.reasonPhrase}");
      }
    } catch (e) {
      setState(() => _isLoading = false);
      print("Payment Verification Error: $e");
      _showSuccessDialog("Payment Received!",
          "Your payment was successful, but we encountered an issue verifying it with our server. Please contact support if your access isn't updated shortly. [${e.toString()}]");
    } finally {
      setState(() {
        selectedPlan = null;
      });
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print("Payment Failed: ${response.code} - ${response.message}");
    _showErrorDialog(
      "Payment Failed",
      "Your payment could not be processed. Please try again or use a different payment method. (Error: ${response.message})",
    );
    setState(() {
      selectedPlan = null;
    });
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print("External Wallet Selected: ${response.walletName}");
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.check_circle, color: successColor, size: 28),
              const SizedBox(width: 10),
              Text(title, style: TextStyle(color: successColor)),
            ],
          ),
          content: Text(message, style: TextStyle(fontSize: 16)),
          actions: <Widget>[
            TextButton(
              child: const Text("OK", style: TextStyle(fontSize: 16)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String title, [String? message]) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.error, color: errorColor, size: 28),
              const SizedBox(width: 10),
              Text(title, style: TextStyle(color: errorColor)),
            ],
          ),
          content: Text(
              message ?? "An unexpected error occurred. Please try again later.",
              style: TextStyle(fontSize: 16)),
          actions: <Widget>[
            TextButton(
              child: const Text("OK", style: TextStyle(fontSize: 16)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text("Popular Plans"),
        backgroundColor: platecolor.withOpacity(0.8),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: const [
                  Color(0xFF265073),
                  Color(0xFF2D9596),
                  Color(0xFF9AD0C2),
                ],
                begin: _gradientBegin,
                end: _gradientEnd,
              ),
            ),
            child: child,
          );
        },
        child: SafeArea(
          child: Stack(
            children: [
              FadeTransition(
                opacity: _fadeAnimation,
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                          decoration: BoxDecoration(
                              color: warningColor.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 5,
                                    offset: Offset(0, 2))
                              ]),
                          child: Text(
                            "! First 100 users can get this ${currency == "INR" ? "₹1" : "\$1"} offer !",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        if (plans.isNotEmpty)
                          CarouselSlider(
                            options: CarouselOptions(
                              height: MediaQuery.of(context).size.height * 0.6,
                              enlargeCenterPage: true,
                              enableInfiniteScroll: false,
                              viewportFraction: 0.8,
                              aspectRatio: 16 / 9,
                              autoPlay: false,
                            ),
                            items: plans.map((plan) {
                              return PlanCard(
                                plan: plan,
                                onTap: handleCardClick,
                                currency: currency,
                              );
                            }).toList(),
                          )
                        else if (!_isLoading)
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.all(20.0),
                              child: Text(
                                "No plans available at the moment.",
                                style: TextStyle(color: Colors.white70, fontSize: 16),
                              ),
                            ),
                          ),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 20,
                right: 20,
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.2),
                      border: Border.all(color: Colors.white.withOpacity(0.4)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Lottie.asset(
                      'assets/animations/privacy_policy.json',
                      width: 60,
                      height: 150,
                      fit: BoxFit.contain,
                      repeat: true,
                    ),
                  ),
                ),
              ),
              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.5),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}