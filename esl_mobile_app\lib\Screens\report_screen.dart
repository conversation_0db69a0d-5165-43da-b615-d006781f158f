import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:my_esl_app/user_provider.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';
import 'privacy_policy_screen.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({super.key});

  @override
  _ReportScreenState createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> {
  final TextEditingController _problemController = TextEditingController();
  File? _image;
  final ImagePicker _picker = ImagePicker();
  final String serverUrl = "https://intervu.in/report";

  Future<void> _pickImage() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _image = File(pickedFile.path);
      });
    }
  }

  Future<void> _sendReport() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    String? userId = userProvider.userId;
    String problemText = _problemController.text.trim();

    if (userId == null || userId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('User not logged in. Please log in again.')),
      );
      return;
    }

    if (problemText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter the problem description.')),
      );
      return;
    }

    try {
      var uri = Uri.parse(serverUrl);
      var request = http.MultipartRequest('POST', uri);
      request.fields['user_id'] = userId;
      request.fields['problem_text'] = problemText;

      if (_image != null) {
        var stream = http.ByteStream(_image!.openRead());
        var length = await _image!.length();
        var multipartFile = http.MultipartFile(
          'image',
          stream,
          length,
          filename: _image!.path.split('/').last,
        );
        request.files.add(multipartFile);
      }

      var response = await request.send();

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Report sent successfully!')),
        );
        _problemController.clear();
        setState(() {
          _image = null;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to send report.')),
        );
      }
    } catch (e) {
      print("Error sending report: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error sending report.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Report an Issue',
          style: TextStyle(color: Color(0xFFc4ecde)),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color.fromARGB(255, 50, 107, 87),
                Color.fromARGB(255, 34, 136, 117),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        elevation: 4,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFFc4ecde),
              Colors.white,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Stack(
          children: [
            // Main content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 5,
                    color: const Color(0xFFc4ecde).withOpacity(0.9),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            'Describe the Issue',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color.fromARGB(255, 50, 107, 87),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Problem Description TextField
                          TextField(
                            controller: _problemController,
                            maxLines: 4,
                            decoration: InputDecoration(
                              labelText: 'Problem Description',
                              hintText: 'Enter your issue...',
                              labelStyle: const TextStyle(
                                color: Color.fromARGB(255, 34, 136, 117),
                              ),
                              hintStyle: const TextStyle(
                                color: Color.fromARGB(255, 34, 136, 117),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: Color.fromARGB(255, 50, 107, 87),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: Color.fromARGB(255, 34, 136, 117),
                                  width: 2,
                                ),
                              ),
                            ),
                            style: const TextStyle(
                              color: Color.fromARGB(255, 34, 136, 117),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Image Preview
                          if (_image != null)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.file(
                                _image!,
                                height: 150,
                                width: double.infinity,
                                fit: BoxFit.cover,
                              ),
                            )
                          else
                            const Text(
                              'No image selected.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color.fromARGB(255, 34, 136, 117),
                              ),
                            ),
                          const SizedBox(height: 8),
                          // Attach Image Button
                          ElevatedButton.icon(
                            onPressed: _pickImage,
                            icon: const Icon(
                              Icons.image,
                              color: Color(0xFFc4ecde),
                            ),
                            label: const Text(
                              'Attach Image',
                              style: TextStyle(color: Color(0xFFc4ecde)),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  const Color.fromARGB(255, 50, 107, 87),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Send Report Button
                          ElevatedButton.icon(
                            onPressed: _sendReport,
                            icon: const Icon(
                              Icons.send,
                              color: Color(0xFFc4ecde),
                            ),
                            label: const Text(
                              'Send Report',
                              style: TextStyle(color: Color(0xFFc4ecde)),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  const Color.fromARGB(255, 34, 136, 117),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(
                      height: 50), // Space below the report container
                ],
              ),
            ),
            // Lottie Animation outside the report container
            Positioned(
              left: 16,
              bottom: 16,
              child: Tooltip(
                message: 'View Privacy Policy',
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: 120,
                          width: 120,
                          child: Lottie.asset(
                            'assets/animations/privacy_policy.json',
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Privacy Policy',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color.fromARGB(255, 50, 107, 87),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
