import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_fortune_wheel/flutter_fortune_wheel.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:audioplayers/audioplayers.dart';

class SpinWheelDialog extends StatefulWidget {
  final List<String> scenarios;
  final Map<String, Map<String, String>> scenarioDetails;
  final Map<String, String> scenarioImageUrls;
  final Function(String) onScenarioSelected;

  const SpinWheelDialog({
    required this.scenarios,
    required this.scenarioDetails,
    required this.scenarioImageUrls,
    required this.onScenarioSelected,
    Key? key,
  }) : super(key: key);

  @override
  _SpinWheelDialogState createState() => _SpinWheelDialogState();
}

class _SpinWheelDialogState extends State<SpinWheelDialog> {
  final StreamController<int> selected = StreamController<int>();
  int? selectedIndex;
  final AudioPlayer _player = AudioPlayer();

  final Color tealLight = const Color.fromARGB(255, 127, 196, 189); // Teal[200]
  final Color tealDark = const Color.fromARGB(255, 19, 164, 147); // Teal[800]

  @override
  void initState() {
    super.initState();
    _spinWheel();
  }

  Future<void> _playSpinSound() async {
    try {
      await _player.play(AssetSource('sounds/spin.wav'), volume: 1.0);
    } catch (e) {
      print('Error playing spin sound: $e');
    }
  }

  @override
  void dispose() {
    selected.close();
    _player.dispose();
    super.dispose();
  }

  void _spinWheel() async {
    final randomIndex = Random().nextInt(widget.scenarios.length);

    setState(() {
      selectedIndex = null; // Reset previous selection
    });

    await _playSpinSound(); // 🔊 Play spin sound

    selected.add(randomIndex); // 🎯 Trigger spin animation

    await Future.delayed(const Duration(seconds: 4)); // Wait for spin to end
    await _player.stop(); // 🔇 Stop sound after spin

    setState(() {
      selectedIndex = randomIndex; // Show selected item
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.96),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            Expanded(
              child: FortuneWheel(
                selected: selected.stream,
                physics: CircularPanPhysics(
                  duration: const Duration(seconds: 4),
                  curve: Curves.decelerate,
                ),
                indicators: const <FortuneIndicator>[
                  FortuneIndicator(
                    alignment: Alignment.topCenter,
                    child: Icon(
                      Icons.arrow_drop_down,
                      color: Colors.teal,
                      size: 58,
                    ),
                  ),
                ],
                items: [
                  for (int i = 0; i < widget.scenarios.length; i++)
                    FortuneItem(
                      style: FortuneItemStyle(
                        color: i.isEven ? tealLight : tealDark,
                        borderColor: Colors.white,
                        borderWidth: 0,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Text(
                          widget.scenarios[i],
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                ],
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              icon: const Icon(Icons.refresh),
              onPressed: _spinWheel,
              label: const Text("Spin Again"),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            if (selectedIndex != null) ...[
              Text(
                'Selected Scenario:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.scenarioImageUrls[widget.scenarios[selectedIndex!]]!,
                  width: 100,
                  height: 100,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: _spinWheel,
                    style: TextButton.styleFrom(foregroundColor: Colors.teal),
                    child: const Text("Spin Again"),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(foregroundColor: Colors.teal),
                    child: const Text("Close"),
                  ),
                  TextButton(
                    onPressed: () {
                      widget.onScenarioSelected(
                        widget.scenarios[selectedIndex!],
                      );
                    },
                    style: TextButton.styleFrom(foregroundColor: Colors.teal),
                    child: const Text("Confirm"),
                  ),
                ],
              )
            ],
          ],
        ),
      ),
    );
  }
}
