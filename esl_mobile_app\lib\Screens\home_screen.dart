import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:my_esl_app/Modules/homophones_screen.dart';
import 'package:my_esl_app/Modules/idioms_screen.dart';
import 'package:my_esl_app/Modules/speaking_screen.dart';
import 'package:my_esl_app/Modules/writing_screen.dart';
import 'package:my_esl_app/constants/app_colors.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:star_menu/star_menu.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../main.dart';
import '../user_provider.dart';
import 'helper_bot.dart';
import '../screens/pro_screen.dart';
import 'explore_screen.dart';
import 'package:my_esl_app/services/tutorial_manager.dart';
import 'package:my_esl_app/widgets/profile_overlay.dart';
import 'package:my_esl_app/Modules/daily_vocabulary_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _swingController;
  late Animation<double> _swingAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late AnimationController _dragController;
  late Animation<double> _dragAnimation;
  late AnimationController _bellBlinkController;
  late Animation<double> _bellBlinkAnimation;
  late ScrollController _scrollController;

  double _scrollOffset = 0.0;
  bool isAtTop = true;
  bool isAtBottom = false;
  double _dragOffsetY = 0.0;
  bool _isProScreenOverlayVisible = false;
  bool isMicVisible = true;
  int _streakCount = 0;
  bool _taskCompletedToday = false;

  final double _ropeInitialHeight = 50.0;
  final double _badgeSize = 100.0;
  final double _pullThreshold = 100.0;
  final String _whatsAppNumber = '+**********';
  final String _whatsAppMessage = 'Hello, I have a query!';
  final GlobalKey _ropeKey = GlobalKey();
  final GlobalKey _editProfileKey = GlobalKey();
  final GlobalKey _bellKey = GlobalKey();
  List<bool> _cardVisibilities = List.generate(6, (_) => false);

  final List<Map<String, dynamic>> categories = [
    {
      'name': 'Speaking',
      'screen': () => const SpeakingPlayground(),
      'asset': 'assets/icons/team_convo.png',
      'assetType': 'image',
    },
    {
      'name': 'Writing',
      'screen': () => const WritingPlaygroundPage(),
      'asset': 'assets/icons/writeicon.png',
      'assetType': 'image',
    },
    {
      'name': 'Homophones',
      'screen': () => const HomophonesScreen(),
      'asset': 'assets/icons/homophoneicon.png',
      'assetType': 'image',
    },
    {
      'name': 'Idioms',
      'screen': () => const IdiomsPage(),
      'asset': 'assets/icons/idiomsicon.png',
      'assetType': 'image',
    },
    {
      'name': 'Vocabulary',
      'screen': () => const ExploreScreen(),
      'asset': 'assets/icons/vocabularyicon.png',
      'assetType': 'image',
    },
    {
      'name': 'Grammar',
      'screen': () => const ExploreScreen(),
      'asset': 'assets/icons/grammaricon.png',
      'assetType': 'image',
    },
  ];

  DateTime? _trialExpiryDate;
  Timer? _trialTimer;
  Duration? _remainingTrialTime;

  @override
  void initState() {
    super.initState();
    _swingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);
    _swingAnimation = Tween<double>(
      begin: vector.radians(-8),
      end: vector.radians(8),
    ).animate(CurvedAnimation(
      parent: _swingController,
      curve: Curves.easeInOutSine,
    ));

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _dragController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _dragAnimation = Tween<double>(begin: 0.0, end: 0.0).animate(
      CurvedAnimation(parent: _dragController, curve: Curves.easeOutBack),
    )..addListener(() {
        setState(() => _dragOffsetY = _dragAnimation.value);
      });

    _bellBlinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);
    _bellBlinkAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(parent: _bellBlinkController, curve: Curves.easeInOut),
    );

    _scrollController = ScrollController()
      ..addListener(() {
        setState(() {
          _scrollOffset = _scrollController.offset;
          isAtTop = _scrollOffset <= 0;
          isAtBottom = _scrollController.hasClients &&
              (_scrollController.position.maxScrollExtent - _scrollOffset <
                  100);
        });
      });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchProfileIfNeeded();
      _animateCards();
      _initTutorial();
      _checkStreaks();
      _fetchTrialDetails();
    });
  }

  Future<void> _fetchTrialDetails() async {
    final token = await const FlutterSecureStorage().read(key: 'access_token');
    if (token == null) return;

    try {
      final paymentDetails = await _fetchPaymentDetails(token);
      if (paymentDetails != null && paymentDetails['expiry_date'] != null) {
        setState(() {
          _trialExpiryDate = DateTime.parse(paymentDetails['expiry_date']);
        });
        _startTrialTimer();
      }
    } catch (e) {
      print('Error fetching trial details: $e');
    }
  }

  void _startTrialTimer() {
    _trialTimer?.cancel();
    _updateRemainingTime();
    _trialTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateRemainingTime();
    });
  }

  void _updateRemainingTime() {
    if (_trialExpiryDate == null) return;
    final now = DateTime.now();
    final remaining = _trialExpiryDate!.difference(now);
    if (remaining.isNegative) {
      setState(() {
        _remainingTrialTime = Duration.zero;
      });
      _trialTimer?.cancel();
    } else {
      setState(() {
        _remainingTrialTime = remaining;
      });
    }
  }

  @override
  void dispose() {
    _trialTimer?.cancel();
    _swingController.dispose();
    _pulseController.dispose();
    _dragController.dispose();
    _bellBlinkController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initTutorial() async {
    await Future.delayed(const Duration(milliseconds: 800));
    if (!mounted) return;
    if (await TutorialManager.isTutorialShown()) return;

    final tutorialCoachMark = TutorialManager.createTutorial(
      context: context,
      ropeKey: _ropeKey,
      editProfileKey: _editProfileKey,
      additionalKeys: [_bellKey],
      additionalDescriptions: [
        'Tap the bell to subscribe to notifications and receive updates!'
      ],
    );
    tutorialCoachMark.show(context: context);
  }

  void _animateCards() async {
    for (int i = 0; i < categories.length; i++) {
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        setState(() => _cardVisibilities[i] = true);
      }
    }
  }

  Future<void> _checkStreaks() async {
    setState(() {
      _streakCount = 3;
      _taskCompletedToday = false;
    });
  }

  Future<void> _fetchProfileIfNeeded() async {
    final token = await const FlutterSecureStorage().read(key: 'access_token');
    if (token == null) {
      print("No access token found.");
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        );
      }
      return;
    }

    try {
      final profile = await _fetchUserProfile(token);
      if (profile != null && mounted) {
        String? finalProfilePicPath = profile['profile_picture'];
        if (finalProfilePicPath != null &&
            !finalProfilePicPath.startsWith('/') &&
            !finalProfilePicPath.startsWith('http')) {
          try {
            final imageBytes = base64Decode(finalProfilePicPath);
            finalProfilePicPath = await _saveImageLocally(imageBytes);
          } catch (e) {
            print("Error decoding/saving base64 profile picture: $e");
            finalProfilePicPath = null;
          }
        }
        await Provider.of<UserProvider>(context, listen: false).setUserDetails(
          userId: profile['user_id']?.toString(),
          phoneNumber: profile['phone_number'],
          firstName: profile['first_name'],
          lastName: profile['last_name'],
          email: profile['email'],
          nationality: profile['nationality'],
          nativeLanguage: profile['native_language'],
          levelOfEnglish: profile['level_of_english'],
          userName: profile['user_name'],
          age: profile['age'],
          profilePicture: finalProfilePicPath,
        );
      }
    } catch (e) {
      print('Error fetching profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not load profile: $e')),
        );
      }
    }
  }

  Future<String?> _saveImageLocally(Uint8List imageBytes) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/profile_${DateTime.now().millisecondsSinceEpoch}.png';
      await File(filePath).writeAsBytes(imageBytes);
      print("Profile image saved to: $filePath");
      return filePath;
    } catch (e) {
      print("Error saving image: $e");
      return null;
    }
  }

  Future<Map<String, dynamic>?> _fetchUserProfile(String token) async {
    try {
      final response = await http.get(
        Uri.parse('http://talktoai.in:4001/user-profile'),
        headers: {'Authorization': 'Bearer $token'},
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        return jsonDecode(utf8.decode(response.bodyBytes));
      }

      if (response.statusCode == 401) {
        final refreshToken =
            await const FlutterSecureStorage().read(key: 'refresh_token');
        if (refreshToken == null) {
          throw Exception("No refresh token found.");
        }
        final refreshResponse = await http
            .post(
              Uri.parse('http://talktoai.in:4001/refresh-token'),
              headers: {'Content-Type': 'application/json'},
              body: jsonEncode({'refresh_token': refreshToken}),
            )
            .timeout(const Duration(seconds: 10));

        if (refreshResponse.statusCode == 200) {
          final newToken = jsonDecode(refreshResponse.body)['access_token'];
          if (newToken != null) {
            print("Token refreshed. Retrying profile fetch.");
            await const FlutterSecureStorage()
                .write(key: 'access_token', value: newToken);
            return await _fetchUserProfile(newToken);
          }
          throw Exception("Refresh response missing access token.");
        }
        throw Exception("Token refresh failed: ${response.statusCode}");
      }
      throw Exception("Failed to load profile: ${response.statusCode}");
    } catch (e) {
      print('Error during profile fetch: $e');
      if (mounted && e.toString().contains('401')) {
        await _performLocalClear(context);
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        );
      }
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> _fetchPaymentDetails(String token) async {
    final response = await http.get(
      Uri.parse('http://talktoai.in:4002/payment-details'),
      headers: {'Authorization': 'Bearer $token'},
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return null;
    }
  }

  Future<void> _clearSession(BuildContext context) async {
    final confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirm Logout'),
            content: const Text(
                'Are you sure you want to log out? This will clear your session data.'),
            actions: [
              TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Cancel')),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: AppColors.red),
                child: const Text('Logout'),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirm || !mounted) return;

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()));

    final token = await const FlutterSecureStorage().read(key: 'access_token');
    if (token != null) {
      try {
        final response = await http.post(
          Uri.parse('http://talktoai.in:4001/clear-session'),
          headers: {'Authorization': 'Bearer $token'},
        ).timeout(const Duration(seconds: 10));
        print(response.statusCode == 200
            ? "Server session cleared."
            : "Failed to clear server session: ${response.statusCode}");
      } catch (e) {
        print('Error clearing session: $e');
      }
    }

    await _performLocalClear(context);
    if (mounted) Navigator.pop(context);
    if (mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        (Route<dynamic> route) => false,
      );
    }
  }

  Future<void> _performLocalClear(BuildContext context) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (userProvider.profilePicture != null) {
      try {
        final file = File(userProvider.profilePicture!);
        if (await file.exists()) await file.delete();
        print("Local profile picture deleted.");
      } catch (e) {
        print("Error deleting profile picture: $e");
      }
    }
    await const FlutterSecureStorage().deleteAll();
    userProvider.clearUserDetails();
    print("Local session cleared.");
  }

  Future<void> _updateUserProfile(
      String token, Map<String, dynamic> profileData) async {
    try {
      profileData.removeWhere((key, value) => value == null);
      final response = await http
          .put(
            Uri.parse('http://talktoai.in:4001/update-profile'),
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json; charset=utf-8'
            },
            body: jsonEncode(profileData),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        print("Profile updated successfully.");
      } else {
        final errorBody = jsonDecode(utf8.decode(response.bodyBytes));
        throw Exception(errorBody['detail'] ??
            'Failed to update profile: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating profile: $e');
      throw Exception("Could not update profile: $e");
    }
  }

  Future<void> _launchWhatsAppChat() async {
    final url = Uri.parse(
        'https://wa.me/$_whatsAppNumber?text=${Uri.encodeComponent(_whatsAppMessage)}');
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not open WhatsApp.');
      }
    } catch (e) {
      print("Error launching WhatsApp: $e");
      if (mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('$e')));
      }
    }
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch $url');
      }
    } catch (e) {
      print("Error launching URL: $e");
      if (mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('$e')));
      }
    }
  }

  void _handleProPullUpdate(DragUpdateDetails details) {
    setState(() {
      _dragOffsetY = max(0, _dragOffsetY + details.delta.dy);
      if (_swingController.isAnimating) _swingController.stop();
    });
  }

  void _handleProPullEnd(DragEndDetails details) {
    if (_dragOffsetY >= _pullThreshold) {
      print("Pro+ pull threshold reached.");
      setState(() {
        _isProScreenOverlayVisible = true;
        _dragOffsetY = 0;
      });
    } else {
      print("Pro+ pull released.");
      _dragController.duration = const Duration(milliseconds: 500);
      _dragAnimation = Tween<double>(begin: _dragOffsetY, end: 0.0).animate(
        CurvedAnimation(parent: _dragController, curve: Curves.easeOutBack),
      );
      _dragController.forward(from: 0.0).then((_) {
        if (!_isProScreenOverlayVisible && !_swingController.isAnimating) {
          _swingController.repeat(reverse: true);
        }
      });
    }
  }

  void _dismissProOverlay() {
    setState(() {
      _isProScreenOverlayVisible = false;
      if (!_swingController.isAnimating) _swingController.repeat(reverse: true);
    });
  }

  Future<void> _subscribeToNotifications() async {
    try {
      final token = await FirebaseMessaging.instance.getToken();
      if (token == null) throw Exception('Failed to get FCM token');

      final response = await http
          .post(
            Uri.parse('http://talktoai.in:4009/subscribe'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'token': token}),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Subscribed to notifications!')));
        }
      } else {
        throw Exception('Failed to subscribe: ${response.statusCode}');
      }
    } catch (e) {
      print('Error subscribing to notifications: $e');
      if (mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('$e')));
      }
    }
  }

  void _showProfileOverlay() {
    showGeneralDialog(
      context: context,
      barrierColor: AppColors.blackWithOpacity(0.7),
      barrierDismissible: true,
      barrierLabel: 'Profile',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) => ProfileOverlay(
        onProfileUpdated: _fetchProfileIfNeeded,
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) =>
          FadeTransition(
        opacity: animation,
        child: ScaleTransition(scale: animation, child: child),
      ),
    );
  }

  Widget _buildIconButton(
      {required IconData icon, required VoidCallback onTap, Color? iconColor}) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return Container(
      width: 35,
      height: 35,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: themeProvider.isDarkMode
            ? AppColors.grey900.withOpacity(0.2)
            : AppColors.lightButtonBackground.withOpacity(0.2),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: icon == Icons.mic
              ? AppColors.primary
              : (iconColor ?? AppColors.primary),
          size: 30,
        ),
        onPressed: onTap,
      ),
    );
  }

  Widget buildSocialMediaItem(
      {required IconData icon,
      required VoidCallback onTap,
      required Color iconColor}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 35,
        height: 35,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.white,
          boxShadow: [
            BoxShadow(
                color: AppColors.blackWithOpacity(0.2),
                blurRadius: 5,
                spreadRadius: 1)
          ],
        ),
        child: Center(child: FaIcon(icon, color: iconColor, size: 25)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final displayName =
        '${userProvider.firstName ?? ''} ${userProvider.lastName ?? ''}'
                .trim()
                .isEmpty
            ? 'Guest'
            : '${userProvider.firstName ?? ''} ${userProvider.lastName ?? ''}'
                .trim();
    final currentRopeHeight =
        max(0.0, _ropeInitialHeight + _dragOffsetY).toDouble();
    final scaleFactor =
        1.0 + 0.1 * (_dragOffsetY / _pullThreshold).clamp(0.0, 1.0);

    final socialMediaItems = [
      // buildSocialMediaItem(
      //     icon: FontAwesomeIcons.whatsapp,
      //     onTap: _launchWhatsAppChat,
      //     iconColor: const Color(0xFF25D366)),
      buildSocialMediaItem(
          icon: FontAwesomeIcons.instagram,
          onTap: () => _launchUrl('https://www.instagram.com/'),
          iconColor: const Color(0xFFC13584)),
      buildSocialMediaItem(
          icon: FontAwesomeIcons.facebook,
          onTap: () => _launchUrl('https://www.facebook.com/'),
          iconColor: const Color(0xFF1877F2)),
      buildSocialMediaItem(
          icon: FontAwesomeIcons.twitter,
          onTap: () => _launchUrl('https://twitter.com/'),
          iconColor: const Color(0xFF1DA1F2)),
    ];
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor:
          themeProvider.isDarkMode ? AppColors.darkBackground : AppColors.white,
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        GestureDetector(
                          key: _editProfileKey,
                          onTap: _showProfileOverlay,
                          child: CircleAvatar(
                            radius: 15,
                            backgroundColor: AppColors.primary,
                            backgroundImage: userProvider.profilePicture !=
                                        null &&
                                    File(userProvider.profilePicture!)
                                        .existsSync()
                                ? FileImage(File(userProvider.profilePicture!))
                                : const AssetImage('assets/user.png'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Hi, $displayName',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Quicksand',
                            color: themeProvider.isDarkMode
                                ? AppColors.white
                                : AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: DailyVocabularyCard(),
                  ),
                  if (_remainingTrialTime != null) 
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: _buildTrialTimerSection(),
                    ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: _buildStreakSection(),
                  ),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'Skills',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: themeProvider.isDarkMode
                                ? AppColors.white
                                : AppColors.primary,
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            _buildAnimatedBellIcon(
                                onTap: _subscribeToNotifications),
                            const SizedBox(width: 12),
                            Padding(
                              padding: const EdgeInsets.only(top: 2),
                              child: _buildThemeToggleButton(),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 22),
                  GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      return _buildCategoryCard(
                        categories[index]['name'],
                        categories[index]['icon'],
                        asset: categories[index]['asset'],
                        assetType: categories[index]['assetType'],
                        onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    categories[index]['screen']())),
                      );
                    },
                  ),
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 25,
            child: SafeArea(
              child: RotationTransition(
                turns: _swingAnimation,
                alignment: Alignment.topCenter,
                child: GestureDetector(
                  key: _ropeKey,
                  onVerticalDragStart: (_) => _swingController.stop(),
                  onVerticalDragUpdate: _handleProPullUpdate,
                  onVerticalDragEnd: _handleProPullEnd,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        'assets/images/rope.png',
                        height: currentRopeHeight,
                        width: 30,
                        fit: BoxFit.fitHeight,
                        errorBuilder: (context, error, stackTrace) => Container(
                          height: currentRopeHeight,
                          width: 30,
                          color: AppColors.brown.withOpacity(0.5),
                          child: const Icon(Icons.error_outline,
                              size: 10, color: AppColors.primary),
                        ),
                      ),
                      Transform.scale(
                        scale: scaleFactor,
                        child: Transform.translate(
                          offset: const Offset(0, -30),
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration:
                                const BoxDecoration(shape: BoxShape.circle),
                            child: Image.asset(
                              'assets/images/pro+.png',
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    shape: BoxShape.circle),
                                child: const Icon(
                                    Icons.workspace_premium_outlined,
                                    size: 10,
                                    color: AppColors.proIconBackground),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (isMicVisible)
            Positioned(
              bottom: 20,
              right: 40,
              child: Transform.translate(
                offset: Offset(isAtTop || isAtBottom ? 0 : 100, 0),
                child: ScaleTransition(
                  scale: _pulseAnimation,
                  child: _buildIconButton(
                    icon: Icons.mic,
                    onTap: () async {
                      setState(() => isMicVisible = false);
                      await showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => const HelperBot(),
                      );
                      if (mounted) setState(() => isMicVisible = true);
                    },
                  ),
                ),
              ),
            ),
          Positioned(
            bottom: 20,
            left: 40,
            child: Transform.translate(
              offset: Offset(isAtTop || isAtBottom ? 0 : -100, 0),
              child: StarMenu(
                params: StarMenuParameters(
                  shape: MenuShape.circle,
                  boundaryBackground: BoundaryBackground(
                      color: AppColors.blackWithOpacity(0.3)),
                  centerOffset: const Offset(0, -100),
                  useScreenCenter: false,
                  linearShapeParams: LinearShapeParams(angle: 270, space: 20),
                ),
                items: socialMediaItems,
                onItemTapped: (index, controller) {
                  (socialMediaItems[index] as GestureDetector).onTap?.call();
                  controller.closeMenu!();
                },
                child: _buildIconButton(icon: Icons.share, onTap: () {}),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 450),
            curve: Curves.easeInOutCubic,
            top: _isProScreenOverlayVisible
                ? 0
                : -MediaQuery.of(context).size.height,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: Material(
              elevation: 10,
              color: themeProvider.isDarkMode
                  ? AppColors.darkProOverlay
                  : AppColors.white,
              child: Stack(
                children: [
                  const ProScreen(),
                  Positioned(
                    top: 8,
                    right: 15,
                    child: SafeArea(
                      child: Container(
                        decoration: BoxDecoration(
                            color: AppColors.primaryWithOpacity(0.4),
                            shape: BoxShape.circle),
                        child: IconButton(
                          icon: const Icon(Icons.close,
                              color: AppColors.notificationIcon),
                          tooltip: 'Close Plans',
                          onPressed: _dismissProOverlay,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrialTimerSection() {
    if (_remainingTrialTime == null || _remainingTrialTime!.inSeconds <= 0) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Provider.of<ThemeProvider>(context).isDarkMode
              ? AppColors.darkStreakSection
              : AppColors.grey100,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
                color: AppColors.blackWithOpacity(0.1),
                blurRadius: 6,
                offset: const Offset(0, 3))
          ],
        ),
        child: Row(
          children: [
            Icon(Icons.timer_off, color: AppColors.red, size: 30),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Trial Expired',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Provider.of<ThemeProvider>(context).isDarkMode
                          ? AppColors.white
                          : AppColors.black,
                    ),
                  ),
                  Text(
                    'Upgrade to continue',
                    style: TextStyle(
                      fontSize: 12,
                      color: Provider.of<ThemeProvider>(context).isDarkMode
                          ? AppColors.grey400
                          : AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: _showProOverlay,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.buttonForeground,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: const Text('Upgrade'),
            ),
          ],
        ),
      );
    }

    final days = _remainingTrialTime!.inDays;
    final hours = _remainingTrialTime!.inHours % 24;
    final minutes = _remainingTrialTime!.inMinutes % 60;
    final seconds = _remainingTrialTime!.inSeconds % 60;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Provider.of<ThemeProvider>(context).isDarkMode
            ? AppColors.darkStreakSection
            : AppColors.grey100,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
              color: AppColors.blackWithOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 3))
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.timer, color: AppColors.primary, size: 30),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Trial Ends In',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Provider.of<ThemeProvider>(context).isDarkMode
                        ? AppColors.white
                        : AppColors.black,
                  ),
                ),
                Text(
                  '$days days, $hours:$minutes:$seconds',
                  style: TextStyle(
                    fontSize: 14,
                    color: Provider.of<ThemeProvider>(context).isDarkMode
                        ? AppColors.grey400
                        : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showProOverlay() {
    setState(() {
      _isProScreenOverlayVisible = true;
    });
  }

  Widget _buildCategoryCard(String title, IconData? icon,
      {String? asset, String? assetType, required VoidCallback onTap}) {
    double imageScale = 1.0;
    return StatefulBuilder(
      builder: (context, setState) => GestureDetector(
        onTapDown: (_) => setState(() => imageScale = 1.1),
        onTapUp: (_) {
          setState(() => imageScale = 1.0);
          Future.delayed(const Duration(milliseconds: 200), onTap);
        },
        onTapCancel: () => setState(() => imageScale = 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: Provider.of<ThemeProvider>(context).isDarkMode
                ? AppColors.darkCategoryCard
                : AppColors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                  color: AppColors.blackWithOpacity(0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 3))
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedScale(
                scale: imageScale,
                duration: const Duration(milliseconds: 150),
                curve: Curves.easeInOut,
                child: (asset != null && assetType == 'image')
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          asset,
                          height: 80 * imageScale,
                          width: 80 * imageScale,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            print('Error loading asset $asset: $error');
                            return Container(
                              height: 80 * imageScale,
                              width: 80 * imageScale,
                              color: AppColors.grey200,
                              child: Icon(Icons.broken_image_outlined,
                                  color: AppColors.primary,
                                  size: 40 * imageScale),
                            );
                          },
                        ),
                      )
                    : (asset != null && assetType == 'video')
                        ? Container(
                            height: 80 * imageScale,
                            width: 80 * imageScale,
                            decoration: BoxDecoration(
                                color: AppColors.grey200,
                                borderRadius: BorderRadius.circular(8)),
                            child: Icon(Icons.videocam,
                                color: AppColors.primary,
                                size: 40 * imageScale),
                          )
                        : Icon(icon ?? Icons.error,
                            size: 40 * imageScale, color: AppColors.primary),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Provider.of<ThemeProvider>(context).isDarkMode
                      ? AppColors.white
                      : AppColors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemeToggleButton() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return GestureDetector(
      onTap: () => themeProvider.toggleTheme(!themeProvider.isDarkMode),
      child: Container(
        width: 40,
        height: 30,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: themeProvider.isDarkMode
              ? AppColors.darkThemeToggle
              : AppColors.primary,
          boxShadow: [
            BoxShadow(
              color: themeProvider.isDarkMode
                  ? AppColors.whiteWithOpacity(0.5)
                  : AppColors.greyWithOpacity(0.7),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            AnimatedPositioned(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              left: themeProvider.isDarkMode ? 3 : 19,
              child: Container(
                width: 18,
                height: 18,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.white,
                ),
                child: Center(
                  child: Icon(
                    themeProvider.isDarkMode
                        ? Icons.nightlight_round
                        : Icons.wb_sunny,
                    size: 12,
                    color: themeProvider.isDarkMode
                        ? AppColors.darkThemeIcon
                        : AppColors.primary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBellIcon({required VoidCallback onTap}) {
    bool isPressed = false;
    return Tooltip(
      message: 'Subscribe to Notifications',
      child: StatefulBuilder(
        builder: (context, setState) => GestureDetector(
          key: _bellKey,
          onTap: () {
            setState(() => isPressed = true);
            Future.delayed(const Duration(milliseconds: 150), () {
              setState(() => isPressed = false);
              onTap();
            });
          },
          child: AnimatedScale(
            scale: isPressed ? 0.9 : 1.0,
            duration: const Duration(milliseconds: 150),
            child: SizedBox(
              width: 30,
              height: 30,
              child: Container(
                decoration: BoxDecoration(
                  gradient: AppColors.tealGradient,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: AnimatedBuilder(
                    animation: _bellBlinkAnimation,
                    builder: (context, child) => Opacity(
                      opacity: _bellBlinkAnimation.value,
                      child: const Icon(Icons.notifications,
                          color: AppColors.notificationIcon, size: 16),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStreakSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Provider.of<ThemeProvider>(context).isDarkMode
            ? AppColors.darkStreakSection
            : AppColors.grey100,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
              color: AppColors.blackWithOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 3))
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.local_fire_department,
                  color:
                      _taskCompletedToday ? AppColors.red : AppColors.grey600,
                  size: 30,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$_streakCount Day Streak',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Provider.of<ThemeProvider>(context).isDarkMode
                              ? AppColors.white
                              : AppColors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        _taskCompletedToday
                            ? 'Great job! Keep it up!'
                            : 'Complete a task to continue your streak!',
                        style: TextStyle(
                          fontSize: 12,
                          color: Provider.of<ThemeProvider>(context).isDarkMode
                              ? AppColors.grey400
                              : AppColors.grey600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: () {
              if (!_taskCompletedToday) {
                setState(() {
                  _streakCount++;
                  _taskCompletedToday = true;
                });
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.buttonForeground,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            child: const Text('Do Task'),
          ),
        ],
      ),
    );
  }
}