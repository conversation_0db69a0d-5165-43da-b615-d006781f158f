import 'package:flutter/material.dart';

class GrammarScreen extends StatelessWidget {
  const GrammarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          const Positioned.fill(
            child: CustomBackground(),
          ),
          Safe<PERSON><PERSON>(
            child: Column(
              children: [
                AppBar(
                  title: const Text('Grammar'),
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                ),
                const Expanded(
                  child: Center(
                    child: Text(
                      'Welcome to Grammar Learning',
                      style: TextStyle(fontSize: 24, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CustomBackground extends StatelessWidget {
  const CustomBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/bg.png'),
          fit: BoxFit.none,
          repeat: ImageRepeat.repeat,
        ),
      ),
    );
  }
}
