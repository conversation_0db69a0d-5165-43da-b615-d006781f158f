import 'package:flutter/material.dart';

class AppColors {
  // Primary Color (Teal)
  static const Color primary = Color(0xFF009688); // Teal 500
  static const Color teal50 = Color(0xFFE0F2F1); // Teal 50
  static const Color teal100 = Color(0xFFB2DFDB); // Teal 100
  static const Color teal200 = Color(0xFF80CBC4); // Teal 200
  static const Color teal300 = Color(0xFF4DB6AC); // Teal 300
  static const Color teal400 = Color(0xFF26A69A); // Teal 400
  static const Color teal600 = Color(0xFF00897B); // Teal 600
  static const Color teal700 = Color(0xFF00796B); // Teal 700
  static const Color teal800 = Color(0xFF00695C); // Teal 800
  static const Color teal900 = Color(0xFF004D40); // Teal 900

  // Secondary Colors
  static const Color white = Color(0xFFFFFFFF); // White
  static const Color black = Color(0xFF000000); // Black
  static const Color red = Color(0xFFF44336); // Red (for streak, logout)
  static const Color brown = Color(0xFF795548); // Brown (rope fallback)
  static const Color accentPurple = Color(0xFF9C27B0); // Purple accent
  static const Color accentTeal = Color(0xFF099DAB); // Teal accent
  static const Color primaryDark = Color(0xFF006064); // Darker teal for dark mode
  static const Color secondaryDark = Color(0xFF37474F); // Dark grey for gradients
  static const Color textLight = Color(0xFFE0E0E0); // Light text for dark mode
  static const Color textDark = Color(0xFF212121); // Dark text for light mode
  static const Color lightBackground = Color(0xFFF1F3F2); // Light button background

  // Grey Shades
  static const Color grey100 = Color(0xFFF5F5F5); // Streak section light mode
  static const Color grey200 = Color(0xFFEEEEEE); // Image fallback
  static const Color grey400 = Color(0xFFB0BEC5); // Text in dark mode
  static const Color grey600 = Color(0xFF757575); // Text in light mode
  static const Color grey900 = Color(0xFF212121); // Button background dark mode

  // UI-Specific Colors
  static const Color lightButtonBackground =
      Color.fromARGB(255, 241, 243, 242); // Button background in light mode
  static const Color notificationIcon =
      Color.fromARGB(255, 245, 244, 242); // Notification and close icons
  static const Color buttonForeground =
      Color.fromARGB(255, 227, 227, 223); // Button text/icon
  static const Color proIconBackground =
      Color.fromARGB(255, 248, 248, 245); // Pro+ icon fallback
  static const Color darkBackground =
      Color.fromARGB(255, 15, 15, 17); // Dark mode scaffold
  static const Color darkProOverlay =
      Color.fromARGB(255, 6, 6, 7); // Dark mode pro overlay
  static const Color darkCategoryCard =
      Color.fromARGB(255, 83, 83, 83); // Dark mode category card
  static const Color darkStreakSection =
      Color.fromARGB(255, 58, 58, 58); // Dark mode streak section
  static const Color darkThemeToggle =
      Color.fromARGB(255, 12, 13, 13); // Dark mode theme toggle
  static const Color darkThemeIcon =
      Color.fromARGB(255, 20, 20, 20); // Dark mode theme toggle icon

  // Opacity Variants
  static Color primaryWithOpacity(double opacity) =>
      primary.withOpacity(opacity);
  static Color blackWithOpacity(double opacity) => black.withOpacity(opacity);
  static Color whiteWithOpacity(double opacity) => white.withOpacity(opacity);
  static Color greyWithOpacity(double opacity) =>
      Color(0xFF9E9E9E).withOpacity(opacity);

  // Gradients
  static const LinearGradient tealGradient = LinearGradient(
    colors: [primary, teal600],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const MaterialAccentColor tealAccent = Colors.tealAccent;
}