import 'dart:convert';

class Section {
  final String part;
  final String? paragraphTitle;
  final Map<String, String>? instructions;
  final List<Question> questions;

  Section({
    required this.part,
    this.paragraphTitle,
    this.instructions,
    required this.questions,
  });

  factory Section.fromJson(Map<String, dynamic> json) {
    return Section(
      part: json['part'] as String,
      paragraphTitle: json['paragraph_title'] as String?,
      instructions: json['instructions'] != null
          ? Map<String, String>.from(json['instructions'])
          : null,
      questions:
          (json['questions'] as List).map((q) => Question.fromJson(q)).toList(),
    );
  }
}

class Question {
  final String id;
  final String type;
  final String text;
  final String? subtype;
  final String? imageUrl;
  final List<String>? options;

  Question({
    required this.id,
    required this.type,
    required this.text,
    this.subtype,
    this.imageUrl,
    this.options,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: json['id'].toString(), // Fix: Convert int to String
      type: json['type'] as String,
      text: json['text'] as String,
      subtype: json['subtype'] as String?,
      imageUrl: json['image_url'] as String?,
      options:
          json['options'] != null ? List<String>.from(json['options']) : null,
    );
  }
}

List<Section> parseJsonData(String jsonString) {
  final Map<String, dynamic> jsonData = jsonDecode(jsonString);
  return (jsonData['sections'] as List)
      .map((section) => Section.fromJson(section))
      .toList();
}
